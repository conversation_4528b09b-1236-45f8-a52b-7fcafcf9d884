# -*- coding: utf-8 -*-
"""
Cyber Shield - Network Monitor
مراقب الشبكة والاتصالات
"""

import psutil
import socket
import requests
import logging
import threading
import time
from typing import Dict, List, Optional, Set
from datetime import datetime
import json

from src.database.db_manager import DatabaseManager
from config.settings import *

logger = logging.getLogger(__name__)

class NetworkMonitor:
    """
    مراقب الشبكة والاتصالات
    """
    
    def __init__(self):
        self.db_manager = DatabaseManager()
        self.is_monitoring = False
        self.monitor_thread = None
        
        # قوائم الحظر والسماح
        self.blocked_ips = set()
        self.allowed_ips = set()
        self.suspicious_ports = {22, 23, 135, 139, 445, 1433, 3389, 5900}
        
        # إحصائيات الشبكة
        self.network_stats = {
            'total_connections': 0,
            'suspicious_connections': 0,
            'blocked_connections': 0,
            'last_update': None
        }
        
        # تحميل قوائم الحظر
        self._load_blocked_ips()
    
    def start_monitoring(self):
        """
        بدء مراقبة الشبكة
        """
        if self.is_monitoring:
            logger.warning("⚠️ مراقبة الشبكة قيد التشغيل بالفعل")
            return
        
        self.is_monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
        
        logger.info("🌐 تم بدء مراقبة الشبكة")
        self._log_activity("NETWORK_MONITORING_STARTED", "تم بدء مراقبة الشبكة")
    
    def stop_monitoring(self):
        """
        إيقاف مراقبة الشبكة
        """
        self.is_monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
        
        logger.info("🛑 تم إيقاف مراقبة الشبكة")
        self._log_activity("NETWORK_MONITORING_STOPPED", "تم إيقاف مراقبة الشبكة")
    
    def _monitor_loop(self):
        """
        حلقة المراقبة الرئيسية
        """
        while self.is_monitoring:
            try:
                self._scan_network_connections()
                self._monitor_network_traffic()
                time.sleep(10)  # فحص كل 10 ثوانٍ
                
            except Exception as e:
                logger.error(f"❌ خطأ في مراقبة الشبكة: {e}")
                time.sleep(30)  # انتظار أطول في حالة الخطأ
    
    def _scan_network_connections(self):
        """
        فحص الاتصالات الشبكية النشطة
        """
        try:
            connections = psutil.net_connections(kind='inet')
            current_time = datetime.now()
            
            for conn in connections:
                if not self.is_monitoring:
                    break
                
                self.network_stats['total_connections'] += 1
                
                # تحليل الاتصال
                connection_info = self._analyze_connection(conn)
                
                if connection_info['is_suspicious']:
                    self.network_stats['suspicious_connections'] += 1
                    self._handle_suspicious_connection(conn, connection_info)
                
                # تسجيل الاتصال في قاعدة البيانات
                self._log_connection(conn, connection_info)
            
            self.network_stats['last_update'] = current_time
            
        except Exception as e:
            logger.error(f"❌ خطأ في فحص الاتصالات: {e}")
    
    def _analyze_connection(self, conn) -> Dict:
        """
        تحليل اتصال شبكي
        """
        connection_info = {
            'is_suspicious': False,
            'risk_level': 'LOW',
            'reasons': [],
            'location_data': None,
            'process_info': None
        }
        
        try:
            # معلومات العملية
            if conn.pid:
                try:
                    process = psutil.Process(conn.pid)
                    connection_info['process_info'] = {
                        'name': process.name(),
                        'exe': process.exe(),
                        'cmdline': ' '.join(process.cmdline())
                    }
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    pass
            
            # فحص العنوان البعيد
            if conn.raddr:
                remote_ip = conn.raddr.ip
                remote_port = conn.raddr.port
                
                # فحص IP المحظورة
                if remote_ip in self.blocked_ips:
                    connection_info['is_suspicious'] = True
                    connection_info['risk_level'] = 'HIGH'
                    connection_info['reasons'].append(f"IP محظور: {remote_ip}")
                
                # فحص المنافذ المشبوهة
                if remote_port in self.suspicious_ports:
                    connection_info['is_suspicious'] = True
                    connection_info['risk_level'] = 'MEDIUM'
                    connection_info['reasons'].append(f"منفذ مشبوه: {remote_port}")
                
                # فحص العناوين المحلية
                if self._is_private_ip(remote_ip):
                    connection_info['risk_level'] = 'LOW'
                else:
                    # الحصول على معلومات الموقع للعناوين الخارجية
                    connection_info['location_data'] = self._get_ip_location(remote_ip)
                    
                    # فحص البلدان المشبوهة
                    if connection_info['location_data']:
                        country = connection_info['location_data'].get('country')
                        if country in ['Unknown', 'Anonymous']:
                            connection_info['is_suspicious'] = True
                            connection_info['risk_level'] = 'MEDIUM'
                            connection_info['reasons'].append("بلد مجهول أو مخفي")
            
            # فحص نوع الاتصال
            if conn.status == 'LISTEN':
                # خدمة تستمع للاتصالات الواردة
                if conn.laddr.port in self.suspicious_ports:
                    connection_info['is_suspicious'] = True
                    connection_info['risk_level'] = 'MEDIUM'
                    connection_info['reasons'].append(f"خدمة تستمع على منفذ مشبوه: {conn.laddr.port}")
            
        except Exception as e:
            logger.debug(f"خطأ في تحليل الاتصال: {e}")
        
        return connection_info
    
    def _handle_suspicious_connection(self, conn, connection_info: Dict):
        """
        معالجة اتصال مشبوه
        """
        try:
            # تسجيل التهديد
            threat_description = f"اتصال مشبوه: {conn.raddr.ip if conn.raddr else 'محلي'}"
            if connection_info['reasons']:
                threat_description += f" - الأسباب: {', '.join(connection_info['reasons'])}"
            
            # حفظ في قاعدة البيانات
            query = """
                INSERT INTO detected_threats (
                    threat_type, threat_name, severity, status, additional_info
                ) VALUES (?, ?, ?, ?, ?)
            """
            
            additional_info = {
                'connection_type': 'NETWORK',
                'local_address': f"{conn.laddr.ip}:{conn.laddr.port}" if conn.laddr else None,
                'remote_address': f"{conn.raddr.ip}:{conn.raddr.port}" if conn.raddr else None,
                'protocol': 'TCP' if conn.type == socket.SOCK_STREAM else 'UDP',
                'status': conn.status,
                'process_info': connection_info.get('process_info'),
                'location_data': connection_info.get('location_data'),
                'reasons': connection_info['reasons']
            }
            
            self.db_manager.execute_query(query, (
                'SUSPICIOUS_CONNECTION',
                threat_description,
                connection_info['risk_level'],
                'DETECTED',
                json.dumps(additional_info)
            ))
            
            logger.warning(f"⚠️ اتصال مشبوه: {threat_description}")
            
        except Exception as e:
            logger.error(f"❌ خطأ في معالجة الاتصال المشبوه: {e}")
    
    def _log_connection(self, conn, connection_info: Dict):
        """
        تسجيل الاتصال في قاعدة البيانات
        """
        try:
            query = """
                INSERT INTO network_monitoring (
                    connection_type, local_address, local_port, remote_address,
                    remote_port, protocol, process_name, process_id, status,
                    is_suspicious, location_data, additional_info
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            
            connection_type = 'OUTGOING' if conn.raddr else 'LISTENING'
            protocol = 'TCP' if conn.type == socket.SOCK_STREAM else 'UDP'
            
            process_name = None
            if connection_info.get('process_info'):
                process_name = connection_info['process_info']['name']
            
            additional_info = {
                'risk_level': connection_info['risk_level'],
                'reasons': connection_info['reasons'],
                'process_info': connection_info.get('process_info')
            }
            
            self.db_manager.execute_query(query, (
                connection_type,
                conn.laddr.ip if conn.laddr else None,
                conn.laddr.port if conn.laddr else None,
                conn.raddr.ip if conn.raddr else None,
                conn.raddr.port if conn.raddr else None,
                protocol,
                process_name,
                conn.pid,
                conn.status,
                connection_info['is_suspicious'],
                json.dumps(connection_info.get('location_data')),
                json.dumps(additional_info)
            ))
            
        except Exception as e:
            logger.debug(f"خطأ في تسجيل الاتصال: {e}")
    
    def _monitor_network_traffic(self):
        """
        مراقبة حركة البيانات الشبكية
        """
        try:
            # الحصول على إحصائيات الشبكة
            net_io = psutil.net_io_counters()
            
            # يمكن إضافة تحليل لحركة البيانات هنا
            # مثل اكتشاف الحركة المشبوهة أو الكثيفة
            
        except Exception as e:
            logger.debug(f"خطأ في مراقبة حركة البيانات: {e}")
    
    def _get_ip_location(self, ip_address: str) -> Optional[Dict]:
        """
        الحصول على معلومات الموقع الجغرافي لعنوان IP
        """
        try:
            if self._is_private_ip(ip_address):
                return None
            
            # استخدام خدمة مجانية للحصول على معلومات الموقع
            response = requests.get(
                f"http://ip-api.com/json/{ip_address}",
                timeout=5
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get('status') == 'success':
                    return {
                        'country': data.get('country'),
                        'country_code': data.get('countryCode'),
                        'region': data.get('regionName'),
                        'city': data.get('city'),
                        'latitude': data.get('lat'),
                        'longitude': data.get('lon'),
                        'isp': data.get('isp'),
                        'org': data.get('org')
                    }
            
        except Exception as e:
            logger.debug(f"خطأ في الحصول على معلومات الموقع لـ {ip_address}: {e}")
        
        return None
    
    def _is_private_ip(self, ip_address: str) -> bool:
        """
        التحقق من كون العنوان IP محلي
        """
        try:
            import ipaddress
            ip = ipaddress.ip_address(ip_address)
            return ip.is_private or ip.is_loopback or ip.is_link_local
        except:
            return False
    
    def _load_blocked_ips(self):
        """
        تحميل قائمة العناوين المحظورة
        """
        # قائمة أساسية للاختبار
        self.blocked_ips = {
            '0.0.0.0',
            '127.0.0.1',  # للاختبار فقط
        }
        
        # يمكن تحميل قوائم إضافية من ملفات أو خدمات خارجية
    
    def block_ip(self, ip_address: str, reason: str = "محظور يدوياً"):
        """
        حظر عنوان IP
        """
        self.blocked_ips.add(ip_address)
        
        # تسجيل الحظر
        self._log_activity(
            "IP_BLOCKED",
            f"تم حظر العنوان {ip_address} - السبب: {reason}"
        )
        
        logger.info(f"🚫 تم حظر العنوان {ip_address}")
    
    def unblock_ip(self, ip_address: str):
        """
        إلغاء حظر عنوان IP
        """
        self.blocked_ips.discard(ip_address)
        
        # تسجيل إلغاء الحظر
        self._log_activity(
            "IP_UNBLOCKED",
            f"تم إلغاء حظر العنوان {ip_address}"
        )
        
        logger.info(f"✅ تم إلغاء حظر العنوان {ip_address}")
    
    def get_network_statistics(self) -> Dict:
        """
        الحصول على إحصائيات الشبكة
        """
        try:
            # إحصائيات النظام
            net_io = psutil.net_io_counters()
            
            stats = {
                'bytes_sent': net_io.bytes_sent,
                'bytes_recv': net_io.bytes_recv,
                'packets_sent': net_io.packets_sent,
                'packets_recv': net_io.packets_recv,
                'errin': net_io.errin,
                'errout': net_io.errout,
                'dropin': net_io.dropin,
                'dropout': net_io.dropout
            }
            
            # إضافة إحصائيات المراقبة
            stats.update(self.network_stats)
            
            return stats
            
        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على إحصائيات الشبكة: {e}")
            return {}
    
    def get_active_connections(self) -> List[Dict]:
        """
        الحصول على الاتصالات النشطة
        """
        try:
            connections = []
            for conn in psutil.net_connections(kind='inet'):
                connection_data = {
                    'local_address': f"{conn.laddr.ip}:{conn.laddr.port}" if conn.laddr else None,
                    'remote_address': f"{conn.raddr.ip}:{conn.raddr.port}" if conn.raddr else None,
                    'status': conn.status,
                    'pid': conn.pid,
                    'protocol': 'TCP' if conn.type == socket.SOCK_STREAM else 'UDP'
                }
                
                # معلومات العملية
                if conn.pid:
                    try:
                        process = psutil.Process(conn.pid)
                        connection_data['process_name'] = process.name()
                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        connection_data['process_name'] = 'Unknown'
                
                connections.append(connection_data)
            
            return connections
            
        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على الاتصالات النشطة: {e}")
            return []
    
    def get_suspicious_connections(self, limit: int = 100) -> List[Dict]:
        """
        الحصول على الاتصالات المشبوهة
        """
        try:
            query = """
                SELECT * FROM network_monitoring 
                WHERE is_suspicious = TRUE 
                ORDER BY detected_at DESC 
                LIMIT ?
            """
            return self.db_manager.execute_query(query, (limit,))
        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على الاتصالات المشبوهة: {e}")
            return []
    
    def _log_activity(self, action: str, description: str):
        """
        تسجيل نشاط في السجل
        """
        try:
            self.db_manager.log_activity(
                user_id=None,
                action=action,
                description=description,
                severity='INFO'
            )
        except Exception as e:
            logger.error(f"❌ خطأ في تسجيل النشاط: {e}")
