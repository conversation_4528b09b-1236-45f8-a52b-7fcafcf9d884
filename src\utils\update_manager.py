# -*- coding: utf-8 -*-
"""
Cyber Shield - Update Manager
مدير التحديثات التلقائية
"""

import os
import json
import logging
import requests
import threading
import time
import hashlib
import zipfile
import shutil
from typing import Dict, List, Optional, Tuple, Callable
from datetime import datetime, timedelta
from pathlib import Path

from src.database.db_manager import DatabaseManager
from src.utils.notification_manager import NotificationManager
from config.settings import *

logger = logging.getLogger(__name__)

class UpdateManager:
    """
    مدير التحديثات التلقائية
    """
    
    def __init__(self):
        self.db_manager = DatabaseManager()
        self.notification_manager = NotificationManager()
        
        # إعدادات التحديث
        self.auto_update_enabled = AUTO_UPDATE_ENABLED if 'AUTO_UPDATE_ENABLED' in globals() else True
        self.check_interval = UPDATE_CHECK_INTERVAL if 'UPDATE_CHECK_INTERVAL' in globals() else 86400
        self.update_server_url = UPDATE_SERVER_URL if 'UPDATE_SERVER_URL' in globals() else ""
        
        # حالة التحديث
        self.is_checking = False
        self.is_updating = False
        self.check_thread = None
        self.last_check = None
        self.available_update = None
        
        # مستمعي الأحداث
        self.update_callbacks = []
        
        # بدء فحص التحديثات التلقائي
        if self.auto_update_enabled:
            self.start_auto_check()
    
    def check_for_updates(self, callback: Callable = None) -> Tuple[bool, Dict]:
        """
        فحص التحديثات المتاحة
        """
        try:
            if self.is_checking:
                return False, {"error": "فحص التحديثات قيد التشغيل بالفعل"}
            
            self.is_checking = True
            self.last_check = datetime.now()
            
            logger.info("🔍 فحص التحديثات المتاحة...")
            
            # الحصول على معلومات الإصدار الحالي
            current_version = self._get_current_version()
            
            # فحص الخادم للتحديثات
            update_info = self._fetch_update_info()
            
            if not update_info:
                self.is_checking = False
                return False, {"error": "فشل في الاتصال بخادم التحديثات"}
            
            # مقارنة الإصدارات
            if self._is_newer_version(update_info.get('version'), current_version):
                self.available_update = update_info
                
                # إشعار بوجود تحديث
                self.notification_manager.send_system_notification(
                    'UPDATE_AVAILABLE',
                    f"تحديث جديد متاح: الإصدار {update_info['version']}",
                    'INFO',
                    update_info
                )
                
                # إشعار المستمعين
                self._notify_update_callbacks('update_available', update_info)
                
                self.is_checking = False
                return True, update_info
            else:
                self.available_update = None
                self.is_checking = False
                return False, {"message": "لا توجد تحديثات متاحة"}
                
        except Exception as e:
            self.is_checking = False
            logger.error(f"❌ خطأ في فحص التحديثات: {e}")
            return False, {"error": f"خطأ في فحص التحديثات: {e}"}
    
    def download_and_install_update(self, update_info: Dict = None, 
                                   callback: Callable = None) -> Tuple[bool, str]:
        """
        تحميل وتثبيت التحديث
        """
        try:
            if self.is_updating:
                return False, "عملية تحديث قيد التشغيل بالفعل"
            
            if not update_info:
                update_info = self.available_update
            
            if not update_info:
                return False, "لا يوجد تحديث متاح"
            
            self.is_updating = True
            
            logger.info(f"📥 بدء تحميل التحديث: {update_info['version']}")
            
            # تحميل التحديث
            download_success, download_path = self._download_update(update_info, callback)
            
            if not download_success:
                self.is_updating = False
                return False, f"فشل في تحميل التحديث: {download_path}"
            
            # التحقق من سلامة الملف
            if not self._verify_update_integrity(download_path, update_info):
                self.is_updating = False
                return False, "فشل في التحقق من سلامة ملف التحديث"
            
            # إنشاء نسخة احتياطية
            backup_success, backup_path = self._create_backup()
            
            if not backup_success:
                self.is_updating = False
                return False, f"فشل في إنشاء نسخة احتياطية: {backup_path}"
            
            # تثبيت التحديث
            install_success, install_message = self._install_update(download_path, update_info)
            
            if install_success:
                # تحديث معلومات الإصدار
                self._update_version_info(update_info['version'])
                
                # إشعار بنجاح التحديث
                self.notification_manager.send_system_notification(
                    'UPDATE_COMPLETE',
                    f"تم تحديث النظام بنجاح إلى الإصدار {update_info['version']}",
                    'INFO'
                )
                
                # تسجيل النشاط
                self.db_manager.log_activity(
                    None,
                    'SYSTEM_UPDATED',
                    f"تم تحديث النظام إلى الإصدار {update_info['version']}",
                    severity='INFO'
                )
                
                # إشعار المستمعين
                self._notify_update_callbacks('update_complete', update_info)
                
                self.is_updating = False
                self.available_update = None
                
                return True, "تم تحديث النظام بنجاح. يرجى إعادة تشغيل التطبيق"
            else:
                # استعادة النسخة الاحتياطية في حالة الفشل
                self._restore_backup(backup_path)
                self.is_updating = False
                return False, f"فشل في تثبيت التحديث: {install_message}"
                
        except Exception as e:
            self.is_updating = False
            logger.error(f"❌ خطأ في تحديث النظام: {e}")
            return False, f"خطأ في تحديث النظام: {e}"
    
    def start_auto_check(self):
        """
        بدء فحص التحديثات التلقائي
        """
        if not self.auto_update_enabled:
            return
        
        if self.check_thread and self.check_thread.is_alive():
            return
        
        self.check_thread = threading.Thread(target=self._auto_check_loop, daemon=True)
        self.check_thread.start()
        
        logger.info("🔄 تم بدء فحص التحديثات التلقائي")
    
    def stop_auto_check(self):
        """
        إيقاف فحص التحديثات التلقائي
        """
        self.auto_update_enabled = False
        logger.info("🛑 تم إيقاف فحص التحديثات التلقائي")
    
    def _auto_check_loop(self):
        """
        حلقة فحص التحديثات التلقائي
        """
        while self.auto_update_enabled:
            try:
                # فحص التحديثات
                has_update, update_info = self.check_for_updates()
                
                if has_update and isinstance(update_info, dict):
                    logger.info(f"🆕 تحديث جديد متاح: {update_info.get('version')}")
                
                # انتظار الفترة المحددة
                time.sleep(self.check_interval)
                
            except Exception as e:
                logger.error(f"❌ خطأ في حلقة فحص التحديثات: {e}")
                time.sleep(3600)  # انتظار ساعة في حالة الخطأ
    
    def _get_current_version(self) -> str:
        """
        الحصول على الإصدار الحالي
        """
        try:
            # محاولة قراءة الإصدار من ملف
            version_file = Path("version.txt")
            if version_file.exists():
                return version_file.read_text().strip()
            
            # الإصدار الافتراضي من الإعدادات
            return APP_VERSION
            
        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على الإصدار الحالي: {e}")
            return APP_VERSION
    
    def _fetch_update_info(self) -> Optional[Dict]:
        """
        جلب معلومات التحديث من الخادم
        """
        try:
            if not self.update_server_url:
                logger.warning("⚠️ رابط خادم التحديثات غير محدد")
                return None
            
            # إرسال طلب للخادم
            response = requests.get(
                f"{self.update_server_url}/releases/latest",
                timeout=30,
                headers={
                    'User-Agent': f'CyberShield/{APP_VERSION}',
                    'Accept': 'application/json'
                }
            )
            
            if response.status_code == 200:
                data = response.json()
                
                # تحويل البيانات لتنسيق موحد
                return {
                    'version': data.get('tag_name', '').replace('v', ''),
                    'name': data.get('name', ''),
                    'description': data.get('body', ''),
                    'download_url': self._get_download_url(data),
                    'file_size': self._get_file_size(data),
                    'checksum': self._get_checksum(data),
                    'published_at': data.get('published_at'),
                    'prerelease': data.get('prerelease', False)
                }
            else:
                logger.error(f"❌ خطأ في الاستجابة من خادم التحديثات: {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"❌ خطأ في جلب معلومات التحديث: {e}")
            return None
    
    def _get_download_url(self, release_data: Dict) -> str:
        """
        الحصول على رابط التحميل
        """
        try:
            assets = release_data.get('assets', [])
            
            # البحث عن ملف التحديث المناسب
            for asset in assets:
                name = asset.get('name', '').lower()
                if name.endswith('.zip') and 'update' in name:
                    return asset.get('browser_download_url', '')
            
            # إذا لم يوجد، استخدام أول ملف zip
            for asset in assets:
                if asset.get('name', '').lower().endswith('.zip'):
                    return asset.get('browser_download_url', '')
            
            return ''
            
        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على رابط التحميل: {e}")
            return ''
    
    def _get_file_size(self, release_data: Dict) -> int:
        """
        الحصول على حجم الملف
        """
        try:
            assets = release_data.get('assets', [])
            if assets:
                return assets[0].get('size', 0)
            return 0
        except:
            return 0
    
    def _get_checksum(self, release_data: Dict) -> str:
        """
        الحصول على checksum الملف
        """
        try:
            # البحث عن ملف checksum
            assets = release_data.get('assets', [])
            for asset in assets:
                name = asset.get('name', '').lower()
                if 'checksum' in name or 'hash' in name:
                    # تحميل ملف checksum
                    response = requests.get(asset.get('browser_download_url', ''), timeout=10)
                    if response.status_code == 200:
                        return response.text.strip()
            
            return ''
            
        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على checksum: {e}")
            return ''
    
    def _is_newer_version(self, new_version: str, current_version: str) -> bool:
        """
        مقارنة الإصدارات
        """
        try:
            def version_tuple(v):
                return tuple(map(int, (v.split("."))))
            
            return version_tuple(new_version) > version_tuple(current_version)
            
        except Exception as e:
            logger.error(f"❌ خطأ في مقارنة الإصدارات: {e}")
            return False
    
    def _download_update(self, update_info: Dict, callback: Callable = None) -> Tuple[bool, str]:
        """
        تحميل ملف التحديث
        """
        try:
            download_url = update_info.get('download_url')
            if not download_url:
                return False, "رابط التحميل غير متاح"
            
            # إنشاء مجلد التحميلات
            downloads_dir = TEMP_DIR / "updates"
            downloads_dir.mkdir(exist_ok=True)
            
            # اسم الملف
            filename = f"update_{update_info['version']}.zip"
            file_path = downloads_dir / filename
            
            # تحميل الملف
            response = requests.get(download_url, stream=True, timeout=60)
            response.raise_for_status()
            
            total_size = int(response.headers.get('content-length', 0))
            downloaded_size = 0
            
            with open(file_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
                        downloaded_size += len(chunk)
                        
                        # تحديث التقدم
                        if callback and total_size > 0:
                            progress = (downloaded_size / total_size) * 100
                            callback('download_progress', {'progress': progress})
            
            logger.info(f"✅ تم تحميل التحديث: {file_path}")
            return True, str(file_path)
            
        except Exception as e:
            logger.error(f"❌ خطأ في تحميل التحديث: {e}")
            return False, str(e)
    
    def _verify_update_integrity(self, file_path: str, update_info: Dict) -> bool:
        """
        التحقق من سلامة ملف التحديث
        """
        try:
            expected_checksum = update_info.get('checksum')
            if not expected_checksum:
                logger.warning("⚠️ لا يوجد checksum للتحقق من سلامة الملف")
                return True  # تجاهل التحقق إذا لم يوجد checksum
            
            # حساب checksum الملف
            file_checksum = self._calculate_file_checksum(file_path)
            
            if file_checksum.lower() == expected_checksum.lower():
                logger.info("✅ تم التحقق من سلامة ملف التحديث")
                return True
            else:
                logger.error("❌ فشل في التحقق من سلامة ملف التحديث")
                return False
                
        except Exception as e:
            logger.error(f"❌ خطأ في التحقق من سلامة الملف: {e}")
            return False
    
    def _calculate_file_checksum(self, file_path: str) -> str:
        """
        حساب checksum للملف
        """
        try:
            hash_sha256 = hashlib.sha256()
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_sha256.update(chunk)
            return hash_sha256.hexdigest()
        except Exception as e:
            logger.error(f"❌ خطأ في حساب checksum: {e}")
            return ""
    
    def _create_backup(self) -> Tuple[bool, str]:
        """
        إنشاء نسخة احتياطية قبل التحديث
        """
        try:
            backup_dir = TEMP_DIR / "backup"
            if backup_dir.exists():
                shutil.rmtree(backup_dir)
            backup_dir.mkdir()
            
            # نسخ الملفات المهمة
            important_files = [
                "main.py",
                "config/",
                "src/",
                "database/",
                "version.txt"
            ]
            
            for item in important_files:
                item_path = Path(item)
                if item_path.exists():
                    if item_path.is_file():
                        shutil.copy2(item_path, backup_dir / item_path.name)
                    else:
                        shutil.copytree(item_path, backup_dir / item_path.name)
            
            logger.info(f"✅ تم إنشاء نسخة احتياطية: {backup_dir}")
            return True, str(backup_dir)
            
        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء النسخة الاحتياطية: {e}")
            return False, str(e)
    
    def _install_update(self, update_file: str, update_info: Dict) -> Tuple[bool, str]:
        """
        تثبيت التحديث
        """
        try:
            # استخراج ملف التحديث
            extract_dir = TEMP_DIR / "update_extract"
            if extract_dir.exists():
                shutil.rmtree(extract_dir)
            extract_dir.mkdir()
            
            with zipfile.ZipFile(update_file, 'r') as zip_ref:
                zip_ref.extractall(extract_dir)
            
            # نسخ الملفات الجديدة
            for item in extract_dir.rglob("*"):
                if item.is_file():
                    relative_path = item.relative_to(extract_dir)
                    target_path = Path(relative_path)
                    
                    # إنشاء المجلدات إذا لم تكن موجودة
                    target_path.parent.mkdir(parents=True, exist_ok=True)
                    
                    # نسخ الملف
                    shutil.copy2(item, target_path)
            
            # تنظيف الملفات المؤقتة
            shutil.rmtree(extract_dir)
            os.remove(update_file)
            
            logger.info("✅ تم تثبيت التحديث بنجاح")
            return True, "تم تثبيت التحديث بنجاح"
            
        except Exception as e:
            logger.error(f"❌ خطأ في تثبيت التحديث: {e}")
            return False, str(e)
    
    def _restore_backup(self, backup_path: str):
        """
        استعادة النسخة الاحتياطية
        """
        try:
            backup_dir = Path(backup_path)
            if not backup_dir.exists():
                return
            
            # استعادة الملفات
            for item in backup_dir.rglob("*"):
                if item.is_file():
                    relative_path = item.relative_to(backup_dir)
                    target_path = Path(relative_path)
                    
                    target_path.parent.mkdir(parents=True, exist_ok=True)
                    shutil.copy2(item, target_path)
            
            logger.info("✅ تم استعادة النسخة الاحتياطية")
            
        except Exception as e:
            logger.error(f"❌ خطأ في استعادة النسخة الاحتياطية: {e}")
    
    def _update_version_info(self, new_version: str):
        """
        تحديث معلومات الإصدار
        """
        try:
            version_file = Path("version.txt")
            version_file.write_text(new_version)
            
            # تحديث في قاعدة البيانات
            query = """
                UPDATE settings SET value = ? 
                WHERE key = 'app_version'
            """
            self.db_manager.execute_query(query, (new_version,))
            
        except Exception as e:
            logger.error(f"❌ خطأ في تحديث معلومات الإصدار: {e}")
    
    def add_update_callback(self, callback: Callable):
        """
        إضافة مستمع لأحداث التحديث
        """
        self.update_callbacks.append(callback)
    
    def _notify_update_callbacks(self, event_type: str, data: Dict):
        """
        إشعار مستمعي أحداث التحديث
        """
        for callback in self.update_callbacks:
            try:
                callback(event_type, data)
            except Exception as e:
                logger.error(f"❌ خطأ في استدعاء callback التحديث: {e}")
    
    def get_update_status(self) -> Dict:
        """
        الحصول على حالة التحديث
        """
        return {
            'current_version': self._get_current_version(),
            'is_checking': self.is_checking,
            'is_updating': self.is_updating,
            'last_check': self.last_check.isoformat() if self.last_check else None,
            'available_update': self.available_update,
            'auto_update_enabled': self.auto_update_enabled
        }
