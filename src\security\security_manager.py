# -*- coding: utf-8 -*-
"""
Cyber Shield - Security Manager
مدير الأمان الرئيسي
"""

import logging
import threading
from typing import Dict, List, Optional, Callable
from datetime import datetime

from src.security.file_monitor import FileMonitor
from src.security.threat_detector import ThreatDetector
from src.security.network_monitor import NetworkMonitor
from src.database.db_manager import DatabaseManager
from config.settings import *

logger = logging.getLogger(__name__)

class SecurityManager:
    """
    مدير الأمان الرئيسي - يدير جميع أنظمة الحماية
    """
    
    def __init__(self):
        self.db_manager = DatabaseManager()
        
        # أنظمة الحماية
        self.file_monitor = FileMonitor()
        self.threat_detector = ThreatDetector()
        self.network_monitor = NetworkMonitor()
        
        # حالة النظام
        self.is_protection_enabled = False
        self.security_level = 'MEDIUM'  # LOW, MEDIUM, HIGH, MAXIMUM
        
        # إحصائيات الأمان
        self.security_stats = {
            'total_threats': 0,
            'threats_blocked': 0,
            'files_monitored': 0,
            'network_events': 0,
            'last_scan': None,
            'protection_uptime': None
        }
        
        # مستمعي الأحداث
        self.event_callbacks = []
        
        # إعداد مستمعي الأحداث
        self._setup_event_listeners()
    
    def start_protection(self):
        """
        بدء جميع أنظمة الحماية
        """
        try:
            if self.is_protection_enabled:
                logger.warning("⚠️ أنظمة الحماية قيد التشغيل بالفعل")
                return True
            
            logger.info("🛡️ بدء تشغيل أنظمة الحماية...")
            
            # بدء مراقبة الملفات
            self.file_monitor.start_monitoring()
            
            # بدء مراقبة الشبكة
            self.network_monitor.start_monitoring()
            
            # تحديث الحالة
            self.is_protection_enabled = True
            self.security_stats['protection_uptime'] = datetime.now()
            
            # تسجيل النشاط
            self._log_security_event(
                'PROTECTION_STARTED',
                'تم بدء تشغيل جميع أنظمة الحماية',
                'INFO'
            )
            
            # إشعار المستمعين
            self._notify_event_listeners('protection_started', {
                'timestamp': datetime.now(),
                'security_level': self.security_level
            })
            
            logger.info("✅ تم بدء تشغيل جميع أنظمة الحماية بنجاح")
            return True
            
        except Exception as e:
            logger.error(f"❌ خطأ في بدء أنظمة الحماية: {e}")
            return False
    
    def stop_protection(self):
        """
        إيقاف جميع أنظمة الحماية
        """
        try:
            if not self.is_protection_enabled:
                logger.warning("⚠️ أنظمة الحماية متوقفة بالفعل")
                return True
            
            logger.info("🛑 إيقاف أنظمة الحماية...")
            
            # إيقاف مراقبة الملفات
            self.file_monitor.stop_monitoring()
            
            # إيقاف مراقبة الشبكة
            self.network_monitor.stop_monitoring()
            
            # تحديث الحالة
            self.is_protection_enabled = False
            
            # تسجيل النشاط
            self._log_security_event(
                'PROTECTION_STOPPED',
                'تم إيقاف جميع أنظمة الحماية',
                'INFO'
            )
            
            # إشعار المستمعين
            self._notify_event_listeners('protection_stopped', {
                'timestamp': datetime.now()
            })
            
            logger.info("🛑 تم إيقاف جميع أنظمة الحماية")
            return True
            
        except Exception as e:
            logger.error(f"❌ خطأ في إيقاف أنظمة الحماية: {e}")
            return False
    
    def start_full_scan(self, callback: Callable = None):
        """
        بدء فحص شامل للنظام
        """
        try:
            logger.info("🔍 بدء الفحص الشامل للنظام")
            
            # إنشاء callback مخصص لتجميع النتائج
            def scan_callback(event_type, data):
                if event_type == 'threat_found':
                    self.security_stats['total_threats'] += 1
                    self._handle_threat_detected(data)
                elif event_type == 'scan_completed':
                    self.security_stats['last_scan'] = datetime.now()
                    logger.info("✅ انتهى الفحص الشامل")
                elif event_type == 'scan_error':
                    logger.error(f"❌ خطأ في الفحص: {data}")
                
                # استدعاء callback المستخدم
                if callback:
                    callback(event_type, data)
            
            # بدء الفحص
            return self.threat_detector.start_full_scan(scan_callback)
            
        except Exception as e:
            logger.error(f"❌ خطأ في بدء الفحص الشامل: {e}")
            return False
    
    def start_quick_scan(self, callback: Callable = None):
        """
        بدء فحص سريع
        """
        try:
            logger.info("⚡ بدء الفحص السريع")
            
            # إنشاء callback مخصص
            def scan_callback(event_type, data):
                if event_type == 'threat_found':
                    self.security_stats['total_threats'] += 1
                    self._handle_threat_detected(data)
                elif event_type == 'scan_completed':
                    self.security_stats['last_scan'] = datetime.now()
                    logger.info("⚡ انتهى الفحص السريع")
                elif event_type == 'scan_error':
                    logger.error(f"❌ خطأ في الفحص: {data}")
                
                # استدعاء callback المستخدم
                if callback:
                    callback(event_type, data)
            
            # بدء الفحص
            return self.threat_detector.start_quick_scan(scan_callback)
            
        except Exception as e:
            logger.error(f"❌ خطأ في بدء الفحص السريع: {e}")
            return False
    
    def set_security_level(self, level: str):
        """
        تعيين مستوى الأمان
        """
        valid_levels = ['LOW', 'MEDIUM', 'HIGH', 'MAXIMUM']
        
        if level not in valid_levels:
            logger.error(f"❌ مستوى أمان غير صحيح: {level}")
            return False
        
        old_level = self.security_level
        self.security_level = level
        
        # تطبيق إعدادات مستوى الأمان
        self._apply_security_level_settings()
        
        # تسجيل التغيير
        self._log_security_event(
            'SECURITY_LEVEL_CHANGED',
            f'تم تغيير مستوى الأمان من {old_level} إلى {level}',
            'INFO'
        )
        
        logger.info(f"🔒 تم تعيين مستوى الأمان إلى: {level}")
        return True
    
    def block_ip_address(self, ip_address: str, reason: str = "محظور يدوياً"):
        """
        حظر عنوان IP
        """
        try:
            self.network_monitor.block_ip(ip_address, reason)
            
            # تسجيل الحظر
            self._log_security_event(
                'IP_BLOCKED',
                f'تم حظر العنوان {ip_address} - السبب: {reason}',
                'WARNING'
            )
            
            return True
            
        except Exception as e:
            logger.error(f"❌ خطأ في حظر العنوان {ip_address}: {e}")
            return False
    
    def quarantine_file(self, file_path: str, reason: str = "ملف مشبوه"):
        """
        عزل ملف مشبوه
        """
        try:
            import shutil
            import os
            from pathlib import Path
            
            # إنشاء مجلد الحجر الصحي
            quarantine_dir = TEMP_DIR / "quarantine"
            quarantine_dir.mkdir(exist_ok=True)
            
            # نقل الملف للحجر الصحي
            file_name = Path(file_path).name
            quarantine_path = quarantine_dir / f"{datetime.now().strftime('%Y%m%d_%H%M%S')}_{file_name}"
            
            shutil.move(file_path, quarantine_path)
            
            # تسجيل العزل
            self._log_security_event(
                'FILE_QUARANTINED',
                f'تم عزل الملف {file_path} إلى {quarantine_path} - السبب: {reason}',
                'WARNING'
            )
            
            logger.info(f"🔒 تم عزل الملف: {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"❌ خطأ في عزل الملف {file_path}: {e}")
            return False
    
    def get_security_dashboard(self) -> Dict:
        """
        الحصول على لوحة معلومات الأمان
        """
        try:
            dashboard = {
                'protection_status': 'ACTIVE' if self.is_protection_enabled else 'INACTIVE',
                'security_level': self.security_level,
                'statistics': self.security_stats.copy(),
                'recent_threats': self.get_recent_threats(10),
                'system_health': self._get_system_health(),
                'network_status': self.network_monitor.get_network_statistics(),
                'file_monitoring_status': 'ACTIVE' if self.file_monitor.is_monitoring else 'INACTIVE'
            }
            
            # تحديث الإحصائيات
            dashboard['statistics'].update({
                'total_threats': len(self.get_all_threats()),
                'files_monitored': len(self.file_monitor.file_hashes),
                'network_events': self.network_monitor.network_stats['total_connections']
            })
            
            return dashboard
            
        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على لوحة معلومات الأمان: {e}")
            return {}
    
    def get_recent_threats(self, limit: int = 50) -> List[Dict]:
        """
        الحصول على التهديدات الأخيرة
        """
        try:
            return self.threat_detector.get_detected_threats(limit)
        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على التهديدات الأخيرة: {e}")
            return []
    
    def get_all_threats(self) -> List[Dict]:
        """
        الحصول على جميع التهديدات
        """
        try:
            query = "SELECT * FROM detected_threats ORDER BY detected_at DESC"
            return self.db_manager.execute_query(query)
        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على جميع التهديدات: {e}")
            return []
    
    def add_event_listener(self, callback: Callable):
        """
        إضافة مستمع للأحداث الأمنية
        """
        self.event_callbacks.append(callback)
    
    def remove_event_listener(self, callback: Callable):
        """
        إزالة مستمع للأحداث الأمنية
        """
        if callback in self.event_callbacks:
            self.event_callbacks.remove(callback)
    
    def _setup_event_listeners(self):
        """
        إعداد مستمعي الأحداث للأنظمة الفرعية
        """
        # مستمع أحداث مراقبة الملفات
        def file_event_handler(event_type, file_path, is_critical):
            self._handle_file_event(event_type, file_path, is_critical)
        
        self.file_monitor.add_callback(file_event_handler)
    
    def _handle_file_event(self, event_type: str, file_path: str, is_critical: bool):
        """
        معالجة أحداث الملفات
        """
        try:
            if is_critical:
                # ملف حساس - تسجيل كتهديد
                threat_data = {
                    'threat_type': 'CRITICAL_FILE_CHANGE',
                    'threat_name': f'تغيير في ملف حساس: {file_path}',
                    'file_path': file_path,
                    'severity': 'HIGH',
                    'event_type': event_type
                }
                
                self._handle_threat_detected(threat_data)
            
            # إشعار المستمعين
            self._notify_event_listeners('file_event', {
                'event_type': event_type,
                'file_path': file_path,
                'is_critical': is_critical,
                'timestamp': datetime.now()
            })
            
        except Exception as e:
            logger.error(f"❌ خطأ في معالجة حدث الملف: {e}")
    
    def _handle_threat_detected(self, threat_data: Dict):
        """
        معالجة اكتشاف تهديد
        """
        try:
            # تحديث الإحصائيات
            self.security_stats['total_threats'] += 1
            
            # تطبيق إجراءات الحماية حسب مستوى الأمان
            if self.security_level in ['HIGH', 'MAXIMUM']:
                # عزل الملف إذا كان موجود
                if threat_data.get('file_path'):
                    self.quarantine_file(
                        threat_data['file_path'],
                        f"تهديد مكتشف: {threat_data.get('threat_name', 'غير محدد')}"
                    )
                    self.security_stats['threats_blocked'] += 1
            
            # إشعار المستمعين
            self._notify_event_listeners('threat_detected', threat_data)
            
            logger.warning(f"⚠️ تم اكتشاف تهديد: {threat_data.get('threat_name', 'غير محدد')}")
            
        except Exception as e:
            logger.error(f"❌ خطأ في معالجة التهديد: {e}")
    
    def _apply_security_level_settings(self):
        """
        تطبيق إعدادات مستوى الأمان
        """
        # يمكن تخصيص إعدادات مختلفة لكل مستوى أمان
        settings = {
            'LOW': {
                'file_monitoring': True,
                'network_monitoring': False,
                'auto_quarantine': False
            },
            'MEDIUM': {
                'file_monitoring': True,
                'network_monitoring': True,
                'auto_quarantine': False
            },
            'HIGH': {
                'file_monitoring': True,
                'network_monitoring': True,
                'auto_quarantine': True
            },
            'MAXIMUM': {
                'file_monitoring': True,
                'network_monitoring': True,
                'auto_quarantine': True
            }
        }
        
        current_settings = settings.get(self.security_level, settings['MEDIUM'])
        
        # تطبيق الإعدادات
        # يمكن إضافة المزيد من التخصيصات هنا
        
        logger.info(f"🔧 تم تطبيق إعدادات مستوى الأمان: {self.security_level}")
    
    def _get_system_health(self) -> Dict:
        """
        الحصول على حالة صحة النظام
        """
        try:
            import psutil
            
            return {
                'cpu_usage': psutil.cpu_percent(),
                'memory_usage': psutil.virtual_memory().percent,
                'disk_usage': psutil.disk_usage('/').percent if os.name != 'nt' else psutil.disk_usage('C:').percent,
                'boot_time': psutil.boot_time(),
                'process_count': len(psutil.pids())
            }
            
        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على حالة النظام: {e}")
            return {}
    
    def _log_security_event(self, action: str, description: str, severity: str = 'INFO'):
        """
        تسجيل حدث أمني
        """
        try:
            self.db_manager.log_activity(
                user_id=None,
                action=action,
                description=description,
                severity=severity
            )
        except Exception as e:
            logger.error(f"❌ خطأ في تسجيل الحدث الأمني: {e}")
    
    def _notify_event_listeners(self, event_type: str, data: Dict):
        """
        إشعار مستمعي الأحداث
        """
        for callback in self.event_callbacks:
            try:
                callback(event_type, data)
            except Exception as e:
                logger.error(f"❌ خطأ في إشعار مستمع الأحداث: {e}")
