# -*- coding: utf-8 -*-
"""
Cyber Shield - Map Generator
مولد الخرائط لعرض المواقع الجغرافية
"""

import os
import json
import logging
from typing import Dict, List, Optional
from datetime import datetime
import webbrowser
import tempfile

from src.utils.location_tracker import LocationTracker
from config.settings import *

logger = logging.getLogger(__name__)

class MapGenerator:
    """
    مولد الخرائط لعرض المواقع الجغرافية
    """
    
    def __init__(self):
        self.location_tracker = LocationTracker()
        self.map_templates_dir = ASSETS_DIR / "map_templates"
        self.map_templates_dir.mkdir(exist_ok=True)
        
        # إنشاء قالب الخريطة الأساسي
        self._create_base_map_template()
    
    def generate_connections_map(self, limit: int = 100) -> str:
        """
        إنشاء خريطة الاتصالات
        """
        try:
            # الحصول على بيانات المواقع
            map_data = self.location_tracker.generate_location_map_data(limit)
            
            if not map_data:
                logger.warning("⚠️ لا توجد بيانات مواقع لعرضها على الخريطة")
                return None
            
            # إنشاء ملف HTML للخريطة
            map_html = self._create_map_html(map_data, "خريطة الاتصالات الشبكية")
            
            # حفظ الخريطة في ملف مؤقت
            map_file = self._save_map_to_file(map_html, "connections_map")
            
            logger.info(f"✅ تم إنشاء خريطة الاتصالات: {map_file}")
            return map_file
            
        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء خريطة الاتصالات: {e}")
            return None
    
    def generate_threats_map(self, limit: int = 50) -> str:
        """
        إنشاء خريطة التهديدات
        """
        try:
            # الحصول على بيانات التهديدات مع المواقع
            threats_data = self._get_threats_location_data(limit)
            
            if not threats_data:
                logger.warning("⚠️ لا توجد بيانات تهديدات مع مواقع لعرضها")
                return None
            
            # إنشاء ملف HTML للخريطة
            map_html = self._create_threats_map_html(threats_data, "خريطة التهديدات الأمنية")
            
            # حفظ الخريطة في ملف مؤقت
            map_file = self._save_map_to_file(map_html, "threats_map")
            
            logger.info(f"✅ تم إنشاء خريطة التهديدات: {map_file}")
            return map_file
            
        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء خريطة التهديدات: {e}")
            return None
    
    def generate_live_map(self) -> str:
        """
        إنشاء خريطة مباشرة للاتصالات النشطة
        """
        try:
            # الحصول على الاتصالات النشطة
            from src.security.network_monitor import NetworkMonitor
            network_monitor = NetworkMonitor()
            active_connections = network_monitor.get_active_connections()
            
            # تحويل الاتصالات لبيانات مواقع
            live_data = []
            for conn in active_connections:
                if conn.get('remote_address'):
                    ip = conn['remote_address'].split(':')[0]
                    location = self.location_tracker.get_ip_location(ip)
                    
                    if location and location.get('latitude') and location.get('longitude'):
                        live_data.append({
                            'latitude': float(location['latitude']),
                            'longitude': float(location['longitude']),
                            'country': location.get('country', 'Unknown'),
                            'city': location.get('city', 'Unknown'),
                            'ip': ip,
                            'process': conn.get('process_name', 'Unknown'),
                            'status': conn.get('status', 'Unknown'),
                            'info': f"{location.get('city', 'Unknown')}, {location.get('country', 'Unknown')} - {conn.get('process_name', 'Unknown')}"
                        })
            
            if not live_data:
                logger.warning("⚠️ لا توجد اتصالات نشطة مع مواقع لعرضها")
                return None
            
            # إنشاء خريطة مباشرة
            map_html = self._create_live_map_html(live_data, "الخريطة المباشرة للاتصالات")
            
            # حفظ الخريطة
            map_file = self._save_map_to_file(map_html, "live_map")
            
            logger.info(f"✅ تم إنشاء الخريطة المباشرة: {map_file}")
            return map_file
            
        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء الخريطة المباشرة: {e}")
            return None
    
    def open_map_in_browser(self, map_file: str):
        """
        فتح الخريطة في المتصفح
        """
        try:
            if os.path.exists(map_file):
                webbrowser.open(f"file://{os.path.abspath(map_file)}")
                logger.info(f"🌐 تم فتح الخريطة في المتصفح: {map_file}")
            else:
                logger.error(f"❌ ملف الخريطة غير موجود: {map_file}")
        except Exception as e:
            logger.error(f"❌ خطأ في فتح الخريطة: {e}")
    
    def _create_map_html(self, map_data: List[Dict], title: str) -> str:
        """
        إنشاء HTML للخريطة العادية
        """
        # حساب المركز
        center_lat = sum(point['latitude'] for point in map_data) / len(map_data)
        center_lng = sum(point['longitude'] for point in map_data) / len(map_data)
        
        # إنشاء نقاط الخريطة
        markers_js = []
        for point in map_data:
            marker_js = f"""
            L.marker([{point['latitude']}, {point['longitude']}])
                .addTo(map)
                .bindPopup(`
                    <div style="direction: rtl; text-align: right;">
                        <h4>{point['info']}</h4>
                        <p><strong>IP:</strong> {point['ip']}</p>
                        <p><strong>البلد:</strong> {point['country']}</p>
                        <p><strong>المدينة:</strong> {point['city']}</p>
                        <p><strong>عدد الاتصالات:</strong> {point['count']}</p>
                    </div>
                `);
            """
            markers_js.append(marker_js)
        
        # قالب HTML
        html_template = f"""
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>{title} - Cyber Shield</title>
            <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
            <style>
                body {{
                    margin: 0;
                    padding: 0;
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                    background: #1a1a1a;
                    color: white;
                }}
                .header {{
                    background: linear-gradient(135deg, #000000, #FF0000, #00FF00);
                    padding: 20px;
                    text-align: center;
                    box-shadow: 0 2px 10px rgba(0,0,0,0.3);
                }}
                .header h1 {{
                    margin: 0;
                    font-size: 24px;
                    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
                }}
                .header .flag {{
                    font-size: 30px;
                    margin-right: 10px;
                }}
                #map {{
                    height: calc(100vh - 100px);
                    width: 100%;
                }}
                .info-panel {{
                    position: absolute;
                    top: 120px;
                    right: 20px;
                    background: rgba(0,0,0,0.8);
                    padding: 15px;
                    border-radius: 10px;
                    z-index: 1000;
                    min-width: 200px;
                }}
                .info-panel h3 {{
                    margin-top: 0;
                    color: #00FF00;
                }}
                .stat {{
                    margin: 5px 0;
                    padding: 5px;
                    background: rgba(255,255,255,0.1);
                    border-radius: 5px;
                }}
            </style>
        </head>
        <body>
            <div class="header">
                <span class="flag">🇵🇸</span>
                <h1>{title}</h1>
                <p>Cyber Shield - Cyper Member | State of Palestine</p>
            </div>
            
            <div class="info-panel">
                <h3>📊 الإحصائيات</h3>
                <div class="stat">📍 عدد المواقع: {len(map_data)}</div>
                <div class="stat">🌍 البلدان: {len(set(point['country'] for point in map_data))}</div>
                <div class="stat">🏙️ المدن: {len(set(point['city'] for point in map_data))}</div>
                <div class="stat">🔗 إجمالي الاتصالات: {sum(point['count'] for point in map_data)}</div>
            </div>
            
            <div id="map"></div>
            
            <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
            <script>
                // إنشاء الخريطة
                var map = L.map('map').setView([{center_lat}, {center_lng}], 2);
                
                // إضافة طبقة الخريطة
                L.tileLayer('https://{{s}}.tile.openstreetmap.org/{{z}}/{{x}}/{{y}}.png', {{
                    attribution: '© OpenStreetMap contributors'
                }}).addTo(map);
                
                // إضافة النقاط
                {chr(10).join(markers_js)}
                
                // إضافة أيقونة مخصصة للنقاط الحساسة
                var redIcon = L.icon({{
                    iconUrl: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyIDJDOC4xMyAyIDUgNS4xMyA1IDlDNSAxNC4yNSAxMiAyMiAxMiAyMkMxMiAyMiAxOSAxNC4yNSAxOSA5QzE5IDUuMTMgMTUuODcgMiAxMiAyWk0xMiAxMS41QzEwLjYyIDExLjUgOS41IDEwLjM4IDkuNSA5QzkuNSA3LjYyIDEwLjYyIDYuNSAxMiA2LjVDMTMuMzggNi41IDE0LjUgNy42MiAxNC41IDlDMTQuNSAxMC4zOCAxMy4zOCAxMS41IDEyIDExLjVaIiBmaWxsPSIjRkYwMDAwIi8+Cjwvc3ZnPgo=',
                    iconSize: [24, 24],
                    iconAnchor: [12, 24],
                    popupAnchor: [0, -24]
                }});
            </script>
        </body>
        </html>
        """
        
        return html_template
    
    def _create_threats_map_html(self, threats_data: List[Dict], title: str) -> str:
        """
        إنشاء HTML لخريطة التهديدات
        """
        if not threats_data:
            return self._create_empty_map_html(title)
        
        # حساب المركز
        center_lat = sum(point['latitude'] for point in threats_data) / len(threats_data)
        center_lng = sum(point['longitude'] for point in threats_data) / len(threats_data)
        
        # إنشاء نقاط التهديدات
        markers_js = []
        for threat in threats_data:
            severity_color = {
                'LOW': '#FFA500',
                'MEDIUM': '#FF6600', 
                'HIGH': '#FF0000',
                'CRITICAL': '#8B0000'
            }.get(threat['severity'], '#FF0000')
            
            marker_js = f"""
            L.marker([{threat['latitude']}, {threat['longitude']}], {{
                icon: L.icon({{
                    iconUrl: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyIDJDOC4xMyAyIDUgNS4xMyA1IDlDNSAxNC4yNSAxMiAyMiAxMiAyMkMxMiAyMiAxOSAxNC4yNSAxOSA5QzE5IDUuMTMgMTUuODcgMiAxMiAyWk0xMiAxMS41QzEwLjYyIDExLjUgOS41IDEwLjM4IDkuNSA5QzkuNSA3LjYyIDEwLjYyIDYuNSAxMiA2LjVDMTMuMzggNi41IDE0LjUgNy42MiAxNC41IDlDMTQuNSAxMC4zOCAxMy4zOCAxMS41IDEyIDExLjVaIiBmaWxsPSJ7severity_color}Ii8+Cjwvc3ZnPgo=',
                    iconSize: [24, 24],
                    iconAnchor: [12, 24],
                    popupAnchor: [0, -24]
                }})
            }})
                .addTo(map)
                .bindPopup(`
                    <div style="direction: rtl; text-align: right;">
                        <h4 style="color: {severity_color};">⚠️ {threat['threat_name']}</h4>
                        <p><strong>النوع:</strong> {threat['threat_type']}</p>
                        <p><strong>الخطورة:</strong> {threat['severity']}</p>
                        <p><strong>الموقع:</strong> {threat['city']}, {threat['country']}</p>
                        <p><strong>IP:</strong> {threat['ip']}</p>
                        <p><strong>التاريخ:</strong> {threat['detected_at']}</p>
                    </div>
                `);
            """
            markers_js.append(marker_js)
        
        # قالب HTML للتهديدات
        html_template = f"""
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>{title} - Cyber Shield</title>
            <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
            <style>
                body {{
                    margin: 0;
                    padding: 0;
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                    background: #1a1a1a;
                    color: white;
                }}
                .header {{
                    background: linear-gradient(135deg, #8B0000, #FF0000, #FF6600);
                    padding: 20px;
                    text-align: center;
                    box-shadow: 0 2px 10px rgba(0,0,0,0.3);
                }}
                .header h1 {{
                    margin: 0;
                    font-size: 24px;
                    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
                }}
                .header .flag {{
                    font-size: 30px;
                    margin-right: 10px;
                }}
                #map {{
                    height: calc(100vh - 100px);
                    width: 100%;
                }}
                .threat-panel {{
                    position: absolute;
                    top: 120px;
                    right: 20px;
                    background: rgba(139,0,0,0.9);
                    padding: 15px;
                    border-radius: 10px;
                    z-index: 1000;
                    min-width: 250px;
                    border: 2px solid #FF0000;
                }}
                .threat-panel h3 {{
                    margin-top: 0;
                    color: #FF6600;
                }}
                .threat-stat {{
                    margin: 5px 0;
                    padding: 8px;
                    background: rgba(255,0,0,0.2);
                    border-radius: 5px;
                    border-left: 4px solid #FF0000;
                }}
                .severity-legend {{
                    margin-top: 15px;
                    padding-top: 15px;
                    border-top: 1px solid #FF0000;
                }}
                .severity-item {{
                    display: flex;
                    align-items: center;
                    margin: 5px 0;
                }}
                .severity-color {{
                    width: 16px;
                    height: 16px;
                    border-radius: 50%;
                    margin-left: 8px;
                }}
            </style>
        </head>
        <body>
            <div class="header">
                <span class="flag">🇵🇸</span>
                <h1>⚠️ {title}</h1>
                <p>Cyber Shield - Cyper Member | State of Palestine</p>
            </div>
            
            <div class="threat-panel">
                <h3>🚨 تحليل التهديدات</h3>
                <div class="threat-stat">📍 عدد التهديدات: {len(threats_data)}</div>
                <div class="threat-stat">🌍 البلدان المتأثرة: {len(set(threat['country'] for threat in threats_data))}</div>
                <div class="threat-stat">🏙️ المدن المتأثرة: {len(set(threat['city'] for threat in threats_data))}</div>
                
                <div class="severity-legend">
                    <h4>مستويات الخطورة:</h4>
                    <div class="severity-item">
                        <div class="severity-color" style="background: #FFA500;"></div>
                        <span>منخفض</span>
                    </div>
                    <div class="severity-item">
                        <div class="severity-color" style="background: #FF6600;"></div>
                        <span>متوسط</span>
                    </div>
                    <div class="severity-item">
                        <div class="severity-color" style="background: #FF0000;"></div>
                        <span>عالي</span>
                    </div>
                    <div class="severity-item">
                        <div class="severity-color" style="background: #8B0000;"></div>
                        <span>حرج</span>
                    </div>
                </div>
            </div>
            
            <div id="map"></div>
            
            <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
            <script>
                // إنشاء الخريطة
                var map = L.map('map').setView([{center_lat}, {center_lng}], 2);
                
                // إضافة طبقة الخريطة الداكنة
                L.tileLayer('https://{{s}}.basemaps.cartocdn.com/dark_all/{{z}}/{{x}}/{{y}}{{r}}.png', {{
                    attribution: '© OpenStreetMap contributors, © CARTO'
                }}).addTo(map);
                
                // إضافة نقاط التهديدات
                {chr(10).join(markers_js)}
            </script>
        </body>
        </html>
        """
        
        return html_template
    
    def _create_live_map_html(self, live_data: List[Dict], title: str) -> str:
        """
        إنشاء HTML للخريطة المباشرة
        """
        # مشابه للخريطة العادية مع إضافة تحديث تلقائي
        return self._create_map_html(live_data, title)
    
    def _create_empty_map_html(self, title: str) -> str:
        """
        إنشاء خريطة فارغة
        """
        html_template = f"""
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>{title} - Cyber Shield</title>
            <style>
                body {{
                    margin: 0;
                    padding: 0;
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                    background: #1a1a1a;
                    color: white;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    height: 100vh;
                }}
                .message {{
                    text-align: center;
                    padding: 40px;
                    background: rgba(0,0,0,0.8);
                    border-radius: 20px;
                    border: 2px solid #FF0000;
                }}
                .flag {{
                    font-size: 50px;
                    margin-bottom: 20px;
                }}
            </style>
        </head>
        <body>
            <div class="message">
                <div class="flag">🇵🇸</div>
                <h1>{title}</h1>
                <p>لا توجد بيانات لعرضها على الخريطة</p>
                <p>Cyber Shield - Cyper Member | State of Palestine</p>
            </div>
        </body>
        </html>
        """
        return html_template

    def _get_threats_location_data(self, limit: int) -> List[Dict]:
        """
        الحصول على بيانات التهديدات مع المواقع
        """
        try:
            from src.database.db_manager import DatabaseManager
            db_manager = DatabaseManager()

            query = """
                SELECT dt.*, nm.location_data
                FROM detected_threats dt
                LEFT JOIN network_monitoring nm ON dt.additional_info LIKE '%' || nm.remote_address || '%'
                WHERE nm.location_data IS NOT NULL
                ORDER BY dt.detected_at DESC
                LIMIT ?
            """

            results = db_manager.execute_query(query, (limit,))
            threats_data = []

            for row in results:
                try:
                    if row['location_data']:
                        location_data = json.loads(row['location_data'])

                        if location_data.get('latitude') and location_data.get('longitude'):
                            threat_point = {
                                'latitude': float(location_data['latitude']),
                                'longitude': float(location_data['longitude']),
                                'country': location_data.get('country', 'Unknown'),
                                'city': location_data.get('city', 'Unknown'),
                                'ip': location_data.get('ip', 'Unknown'),
                                'threat_type': row['threat_type'],
                                'threat_name': row['threat_name'],
                                'severity': row['severity'],
                                'detected_at': row['detected_at']
                            }
                            threats_data.append(threat_point)
                except:
                    continue

            return threats_data

        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على بيانات التهديدات: {e}")
            return []

    def _save_map_to_file(self, html_content: str, map_name: str) -> str:
        """
        حفظ الخريطة في ملف
        """
        try:
            # إنشاء اسم ملف فريد
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{map_name}_{timestamp}.html"

            # حفظ في مجلد مؤقت
            map_file = TEMP_DIR / filename

            with open(map_file, 'w', encoding='utf-8') as f:
                f.write(html_content)

            return str(map_file)

        except Exception as e:
            logger.error(f"❌ خطأ في حفظ الخريطة: {e}")
            return None
