# -*- coding: utf-8 -*-
"""
Cyber Shield - Database Manager
مدير قاعدة البيانات
"""

import sqlite3
import hashlib
import logging
from datetime import datetime, timedelta
from pathlib import Path
from typing import Optional, Dict, List, Any
import json

from config.settings import DATABASE_PATH, SECRET_KEY

logger = logging.getLogger(__name__)

class DatabaseManager:
    """
    مدير قاعدة البيانات الرئيسي
    """
    
    def __init__(self):
        self.db_path = DATABASE_PATH
        self.connection = None
        
    def get_connection(self) -> sqlite3.Connection:
        """
        الحصول على اتصال قاعدة البيانات
        """
        if self.connection is None:
            self.connection = sqlite3.connect(
                self.db_path,
                check_same_thread=False,
                timeout=30.0
            )
            self.connection.row_factory = sqlite3.Row
            # تفعيل المفاتيح الخارجية
            self.connection.execute("PRAGMA foreign_keys = ON")
        return self.connection
    
    def close_connection(self):
        """
        إغلاق اتصال قاعدة البيانات
        """
        if self.connection:
            self.connection.close()
            self.connection = None
    
    def initialize_database(self):
        """
        تهيئة قاعدة البيانات وإنشاء الجداول
        """
        logger.info("🗄️ تهيئة قاعدة البيانات...")
        
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            
            # إنشاء جدول المستخدمين
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS users (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    username TEXT UNIQUE NOT NULL,
                    email TEXT UNIQUE NOT NULL,
                    password_hash TEXT NOT NULL,
                    salt TEXT NOT NULL,
                    full_name TEXT NOT NULL,
                    phone TEXT,
                    is_admin BOOLEAN DEFAULT FALSE,
                    is_active BOOLEAN DEFAULT TRUE,
                    email_verified BOOLEAN DEFAULT FALSE,
                    two_factor_enabled BOOLEAN DEFAULT FALSE,
                    two_factor_secret TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    last_login TIMESTAMP,
                    login_attempts INTEGER DEFAULT 0,
                    locked_until TIMESTAMP,
                    profile_image TEXT
                )
            """)
            
            # إنشاء جدول الجلسات
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS sessions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    session_token TEXT UNIQUE NOT NULL,
                    ip_address TEXT,
                    user_agent TEXT,
                    location_data TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    expires_at TIMESTAMP NOT NULL,
                    is_active BOOLEAN DEFAULT TRUE,
                    FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
                )
            """)
            
            # إنشاء جدول رموز التحقق
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS verification_codes (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    code TEXT NOT NULL,
                    code_type TEXT NOT NULL, -- 'email_verification', 'password_reset', '2fa'
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    expires_at TIMESTAMP NOT NULL,
                    used BOOLEAN DEFAULT FALSE,
                    FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
                )
            """)
            
            # إنشاء جدول سجل الأنشطة
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS activity_logs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER,
                    action TEXT NOT NULL,
                    description TEXT,
                    ip_address TEXT,
                    user_agent TEXT,
                    location_data TEXT,
                    severity TEXT DEFAULT 'INFO', -- 'INFO', 'WARNING', 'ERROR', 'CRITICAL'
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    additional_data TEXT, -- JSON data
                    FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE SET NULL
                )
            """)
            
            # إنشاء جدول التهديدات المكتشفة
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS detected_threats (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    threat_type TEXT NOT NULL,
                    threat_name TEXT NOT NULL,
                    file_path TEXT,
                    process_name TEXT,
                    severity TEXT NOT NULL, -- 'LOW', 'MEDIUM', 'HIGH', 'CRITICAL'
                    status TEXT DEFAULT 'DETECTED', -- 'DETECTED', 'QUARANTINED', 'REMOVED', 'IGNORED'
                    description TEXT,
                    detected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    resolved_at TIMESTAMP,
                    resolved_by INTEGER,
                    additional_info TEXT, -- JSON data
                    FOREIGN KEY (resolved_by) REFERENCES users (id) ON DELETE SET NULL
                )
            """)
            
            # إنشاء جدول مراقبة الملفات
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS file_monitoring (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    file_path TEXT NOT NULL,
                    file_hash TEXT,
                    file_size INTEGER,
                    action TEXT NOT NULL, -- 'CREATED', 'MODIFIED', 'DELETED', 'MOVED'
                    old_path TEXT,
                    new_path TEXT,
                    detected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    user_id INTEGER,
                    process_name TEXT,
                    is_suspicious BOOLEAN DEFAULT FALSE,
                    FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE SET NULL
                )
            """)
            
            # إنشاء جدول مراقبة الشبكة
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS network_monitoring (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    connection_type TEXT NOT NULL, -- 'INCOMING', 'OUTGOING'
                    local_address TEXT,
                    local_port INTEGER,
                    remote_address TEXT,
                    remote_port INTEGER,
                    protocol TEXT, -- 'TCP', 'UDP'
                    process_name TEXT,
                    process_id INTEGER,
                    status TEXT, -- 'ESTABLISHED', 'LISTENING', 'BLOCKED'
                    is_suspicious BOOLEAN DEFAULT FALSE,
                    detected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    location_data TEXT, -- JSON with geolocation info
                    additional_info TEXT -- JSON data
                )
            """)
            
            # إنشاء جدول الإعدادات
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS settings (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    key TEXT UNIQUE NOT NULL,
                    value TEXT NOT NULL,
                    description TEXT,
                    category TEXT DEFAULT 'general',
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_by INTEGER,
                    FOREIGN KEY (updated_by) REFERENCES users (id) ON DELETE SET NULL
                )
            """)
            
            # إنشاء الفهارس لتحسين الأداء
            indexes = [
                "CREATE INDEX IF NOT EXISTS idx_users_username ON users(username)",
                "CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)",
                "CREATE INDEX IF NOT EXISTS idx_sessions_token ON sessions(session_token)",
                "CREATE INDEX IF NOT EXISTS idx_sessions_user_id ON sessions(user_id)",
                "CREATE INDEX IF NOT EXISTS idx_activity_logs_user_id ON activity_logs(user_id)",
                "CREATE INDEX IF NOT EXISTS idx_activity_logs_created_at ON activity_logs(created_at)",
                "CREATE INDEX IF NOT EXISTS idx_threats_detected_at ON detected_threats(detected_at)",
                "CREATE INDEX IF NOT EXISTS idx_file_monitoring_path ON file_monitoring(file_path)",
                "CREATE INDEX IF NOT EXISTS idx_network_monitoring_detected_at ON network_monitoring(detected_at)"
            ]
            
            for index_sql in indexes:
                cursor.execute(index_sql)
            
            # إدراج الإعدادات الافتراضية
            self._insert_default_settings(cursor)
            
            # إنشاء مستخدم المدير الافتراضي
            self._create_default_admin(cursor)
            
            conn.commit()
            logger.info("✅ تم تهيئة قاعدة البيانات بنجاح")
            
        except Exception as e:
            logger.error(f"❌ خطأ في تهيئة قاعدة البيانات: {e}")
            raise
    
    def _insert_default_settings(self, cursor):
        """
        إدراج الإعدادات الافتراضية
        """
        default_settings = [
            ('app_version', '1.0.0', 'إصدار التطبيق', 'system'),
            ('security_level', 'HIGH', 'مستوى الأمان', 'security'),
            ('auto_scan_enabled', 'true', 'تفعيل الفحص التلقائي', 'security'),
            ('email_notifications', 'true', 'إشعارات البريد الإلكتروني', 'notifications'),
            ('desktop_notifications', 'true', 'إشعارات سطح المكتب', 'notifications'),
            ('sound_alerts', 'true', 'التنبيهات الصوتية', 'notifications'),
            ('theme_mode', 'dark', 'وضع المظهر', 'appearance'),
            ('language', 'ar', 'اللغة', 'appearance'),
            ('auto_update', 'true', 'التحديث التلقائي', 'system'),
            ('location_tracking', 'true', 'تتبع الموقع', 'privacy')
        ]
        
        for key, value, description, category in default_settings:
            cursor.execute("""
                INSERT OR IGNORE INTO settings (key, value, description, category)
                VALUES (?, ?, ?, ?)
            """, (key, value, description, category))
    
    def _create_default_admin(self, cursor):
        """
        إنشاء مستخدم المدير الافتراضي
        """
        # التحقق من وجود مدير
        cursor.execute("SELECT COUNT(*) FROM users WHERE is_admin = TRUE")
        admin_count = cursor.fetchone()[0]
        
        if admin_count == 0:
            from src.auth.password_manager import PasswordManager
            from config.settings import DEFAULT_ADMIN_USERNAME, DEFAULT_ADMIN_PASSWORD, DEFAULT_ADMIN_EMAIL, DEFAULT_ADMIN_FULL_NAME

            pwd_manager = PasswordManager()

            # تشفير كلمة المرور الجديدة
            password_hash, salt = pwd_manager.hash_password(DEFAULT_ADMIN_PASSWORD)

            cursor.execute("""
                INSERT INTO users (
                    username, email, password_hash, salt, full_name,
                    is_admin, is_active, email_verified
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                DEFAULT_ADMIN_USERNAME,
                DEFAULT_ADMIN_EMAIL,
                password_hash,
                salt,
                DEFAULT_ADMIN_FULL_NAME,
                True,
                True,
                True
            ))

            logger.info("👨‍💼 تم إنشاء حساب المدير الافتراضي")
            logger.info(f"📧 البريد الإلكتروني: {DEFAULT_ADMIN_EMAIL}")
            logger.info(f"🔑 كلمة المرور: {DEFAULT_ADMIN_PASSWORD}")

    def execute_query(self, query: str, params: tuple = None) -> List[Dict]:
        """
        تنفيذ استعلام وإرجاع النتائج
        """
        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)

            if query.strip().upper().startswith('SELECT'):
                rows = cursor.fetchall()
                return [dict(row) for row in rows]
            else:
                conn.commit()
                return []

        except Exception as e:
            logger.error(f"❌ خطأ في تنفيذ الاستعلام: {e}")
            raise

    def get_user_by_username(self, username: str) -> Optional[Dict]:
        """
        الحصول على مستخدم بواسطة اسم المستخدم
        """
        query = "SELECT * FROM users WHERE username = ? AND is_active = TRUE"
        results = self.execute_query(query, (username,))
        return results[0] if results else None

    def get_user_by_email(self, email: str) -> Optional[Dict]:
        """
        الحصول على مستخدم بواسطة البريد الإلكتروني
        """
        query = "SELECT * FROM users WHERE email = ? AND is_active = TRUE"
        results = self.execute_query(query, (email,))
        return results[0] if results else None

    def create_user(self, user_data: Dict) -> int:
        """
        إنشاء مستخدم جديد
        """
        query = """
            INSERT INTO users (
                username, email, password_hash, salt, full_name,
                phone, is_admin, email_verified, two_factor_enabled
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        """

        conn = self.get_connection()
        cursor = conn.cursor()

        cursor.execute(query, (
            user_data['username'],
            user_data['email'],
            user_data['password_hash'],
            user_data['salt'],
            user_data['full_name'],
            user_data.get('phone'),
            user_data.get('is_admin', False),
            user_data.get('email_verified', False),
            user_data.get('two_factor_enabled', False)
        ))

        conn.commit()
        return cursor.lastrowid

    def log_activity(self, user_id: Optional[int], action: str, description: str = None,
                    ip_address: str = None, severity: str = 'INFO', additional_data: Dict = None):
        """
        تسجيل نشاط في السجل
        """
        query = """
            INSERT INTO activity_logs (
                user_id, action, description, ip_address, severity, additional_data
            ) VALUES (?, ?, ?, ?, ?, ?)
        """

        additional_json = json.dumps(additional_data) if additional_data else None

        self.execute_query(query, (
            user_id, action, description, ip_address, severity, additional_json
        ))
