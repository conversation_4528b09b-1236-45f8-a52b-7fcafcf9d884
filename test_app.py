# -*- coding: utf-8 -*-
"""
Cyber Shield - Test Suite
مجموعة اختبارات شاملة للتطبيق
"""

import sys
import os
import unittest
import tempfile
import shutil
from pathlib import Path
import sqlite3
import threading
import time

# إضافة مسار المشروع
sys.path.insert(0, str(Path(__file__).parent))

from config.settings import *
from src.database.db_manager import DatabaseManager
from src.auth.auth_manager import AuthenticationManager
from src.auth.password_manager import PasswordManager
from src.security.security_manager import SecurityManager
from src.utils.notification_manager import NotificationManager

class TestDatabaseManager(unittest.TestCase):
    """اختبارات مدير قاعدة البيانات"""
    
    def setUp(self):
        """إعداد الاختبار"""
        self.test_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        self.test_db.close()
        self.db_manager = DatabaseManager(self.test_db.name)
        self.db_manager.initialize_database()
    
    def tearDown(self):
        """تنظيف بعد الاختبار"""
        try:
            os.unlink(self.test_db.name)
        except:
            pass
    
    def test_database_creation(self):
        """اختبار إنشاء قاعدة البيانات"""
        self.assertTrue(os.path.exists(self.test_db.name))
        
        # التحقق من وجود الجداول
        conn = sqlite3.connect(self.test_db.name)
        cursor = conn.cursor()
        
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall()]
        
        expected_tables = [
            'users', 'sessions', 'activity_logs', 'detected_threats',
            'file_monitoring', 'network_monitoring', 'settings'
        ]
        
        for table in expected_tables:
            self.assertIn(table, tables, f"الجدول {table} غير موجود")
        
        conn.close()
    
    def test_user_operations(self):
        """اختبار عمليات المستخدمين"""
        # إنشاء مستخدم
        user_data = {
            'username': 'test_user',
            'email': '<EMAIL>',
            'password_hash': 'hashed_password',
            'salt': 'salt123',
            'full_name': 'Test User'
        }
        
        user_id = self.db_manager.create_user(user_data)
        self.assertIsNotNone(user_id)
        
        # البحث عن المستخدم
        user = self.db_manager.get_user_by_username('test_user')
        self.assertIsNotNone(user)
        self.assertEqual(user['email'], '<EMAIL>')
        
        # البحث بالبريد الإلكتروني
        user = self.db_manager.get_user_by_email('<EMAIL>')
        self.assertIsNotNone(user)
        self.assertEqual(user['username'], 'test_user')

class TestPasswordManager(unittest.TestCase):
    """اختبارات مدير كلمات المرور"""
    
    def setUp(self):
        self.password_manager = PasswordManager()
    
    def test_password_hashing(self):
        """اختبار تشفير كلمات المرور"""
        password = "test_password_123"
        hash_result, salt = self.password_manager.hash_password(password)
        
        self.assertIsNotNone(hash_result)
        self.assertIsNotNone(salt)
        self.assertNotEqual(hash_result, password)
    
    def test_password_verification(self):
        """اختبار التحقق من كلمات المرور"""
        password = "test_password_123"
        hash_result, salt = self.password_manager.hash_password(password)
        
        # كلمة مرور صحيحة
        self.assertTrue(
            self.password_manager.verify_password(password, hash_result, salt)
        )
        
        # كلمة مرور خاطئة
        self.assertFalse(
            self.password_manager.verify_password("wrong_password", hash_result, salt)
        )
    
    def test_password_strength(self):
        """اختبار قوة كلمات المرور"""
        # كلمة مرور قوية
        strong_password = "StrongP@ssw0rd123"
        is_valid, errors = self.password_manager.validate_password_strength(strong_password)
        self.assertTrue(is_valid)
        self.assertEqual(len(errors), 0)
        
        # كلمة مرور ضعيفة
        weak_password = "123"
        is_valid, errors = self.password_manager.validate_password_strength(weak_password)
        self.assertFalse(is_valid)
        self.assertGreater(len(errors), 0)

class TestAuthenticationManager(unittest.TestCase):
    """اختبارات مدير المصادقة"""
    
    def setUp(self):
        self.test_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        self.test_db.close()
        self.db_manager = DatabaseManager(self.test_db.name)
        self.db_manager.initialize_database()
        self.auth_manager = AuthenticationManager()
        self.auth_manager.db_manager = self.db_manager
    
    def tearDown(self):
        try:
            os.unlink(self.test_db.name)
        except:
            pass
    
    def test_user_registration(self):
        """اختبار تسجيل المستخدمين"""
        success, message, user_id = self.auth_manager.register_user(
            username="test_user",
            email="<EMAIL>",
            password="StrongP@ssw0rd123",
            full_name="Test User"
        )
        
        self.assertTrue(success)
        self.assertIsNotNone(user_id)
        
        # محاولة تسجيل نفس المستخدم مرة أخرى
        success, message, user_id = self.auth_manager.register_user(
            username="test_user",
            email="<EMAIL>",
            password="StrongP@ssw0rd123",
            full_name="Test User 2"
        )
        
        self.assertFalse(success)
    
    def test_user_login(self):
        """اختبار تسجيل الدخول"""
        # تسجيل مستخدم أولاً
        self.auth_manager.register_user(
            username="test_user",
            email="<EMAIL>",
            password="StrongP@ssw0rd123",
            full_name="Test User"
        )
        
        # تسجيل دخول صحيح
        success, message, user_data = self.auth_manager.login_user(
            "test_user", "StrongP@ssw0rd123", "127.0.0.1"
        )
        
        self.assertTrue(success)
        self.assertIsNotNone(user_data)
        self.assertEqual(user_data['username'], 'test_user')
        
        # تسجيل دخول خاطئ
        success, message, user_data = self.auth_manager.login_user(
            "test_user", "wrong_password", "127.0.0.1"
        )
        
        self.assertFalse(success)

class TestSecurityManager(unittest.TestCase):
    """اختبارات مدير الأمان"""
    
    def setUp(self):
        self.security_manager = SecurityManager()
    
    def test_protection_start_stop(self):
        """اختبار بدء وإيقاف الحماية"""
        # بدء الحماية
        result = self.security_manager.start_protection()
        self.assertTrue(result)
        self.assertTrue(self.security_manager.is_protection_enabled)
        
        # إيقاف الحماية
        result = self.security_manager.stop_protection()
        self.assertTrue(result)
        self.assertFalse(self.security_manager.is_protection_enabled)
    
    def test_security_level(self):
        """اختبار مستويات الأمان"""
        # تعيين مستوى أمان صحيح
        result = self.security_manager.set_security_level('HIGH')
        self.assertTrue(result)
        self.assertEqual(self.security_manager.security_level, 'HIGH')
        
        # تعيين مستوى أمان خاطئ
        result = self.security_manager.set_security_level('INVALID')
        self.assertFalse(result)

class TestNotificationManager(unittest.TestCase):
    """اختبارات مدير التنبيهات"""
    
    def setUp(self):
        self.notification_manager = NotificationManager()
        # إيقاف المعالج التلقائي للاختبار
        self.notification_manager.stop_notification_processor()
    
    def test_notification_creation(self):
        """اختبار إنشاء التنبيهات"""
        initial_count = len(self.notification_manager.notification_queue)
        
        self.notification_manager.send_notification(
            'TEST',
            'Test Notification',
            'This is a test notification',
            'INFO'
        )
        
        self.assertEqual(
            len(self.notification_manager.notification_queue),
            initial_count + 1
        )
    
    def test_security_alert(self):
        """اختبار التنبيهات الأمنية"""
        initial_count = len(self.notification_manager.notification_queue)
        
        self.notification_manager.send_security_alert(
            'TEST_THREAT',
            'Test Threat',
            'HIGH'
        )
        
        self.assertEqual(
            len(self.notification_manager.notification_queue),
            initial_count + 1
        )

class TestIntegration(unittest.TestCase):
    """اختبارات التكامل"""
    
    def setUp(self):
        self.test_dir = tempfile.mkdtemp()
        self.test_db = os.path.join(self.test_dir, 'test.db')
        
        # إعداد مدير قاعدة البيانات
        self.db_manager = DatabaseManager(self.test_db)
        self.db_manager.initialize_database()
        
        # إعداد مدير المصادقة
        self.auth_manager = AuthenticationManager()
        self.auth_manager.db_manager = self.db_manager
    
    def tearDown(self):
        try:
            shutil.rmtree(self.test_dir)
        except:
            pass
    
    def test_full_user_workflow(self):
        """اختبار سير العمل الكامل للمستخدم"""
        # 1. تسجيل مستخدم جديد
        success, message, user_id = self.auth_manager.register_user(
            username="integration_user",
            email="<EMAIL>",
            password="IntegrationP@ss123",
            full_name="Integration Test User"
        )
        
        self.assertTrue(success)
        self.assertIsNotNone(user_id)
        
        # 2. تسجيل الدخول
        success, message, user_data = self.auth_manager.login_user(
            "integration_user", "IntegrationP@ss123", "127.0.0.1"
        )
        
        self.assertTrue(success)
        self.assertIsNotNone(user_data)
        self.assertIn('session_token', user_data)
        
        # 3. التحقق من الجلسة
        from src.auth.session_manager import SessionManager
        session_manager = SessionManager()
        session_manager.db_manager = self.db_manager
        
        session_data = session_manager.validate_session(user_data['session_token'])
        self.assertIsNotNone(session_data)
        
        # 4. تسجيل الخروج
        success = session_manager.invalidate_session(user_data['session_token'])
        self.assertTrue(success)
        
        # 5. التحقق من إلغاء الجلسة
        session_data = session_manager.validate_session(user_data['session_token'])
        self.assertIsNone(session_data)

def run_performance_tests():
    """اختبارات الأداء"""
    print("🚀 تشغيل اختبارات الأداء...")
    
    # اختبار أداء قاعدة البيانات
    test_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
    test_db.close()
    
    try:
        db_manager = DatabaseManager(test_db.name)
        db_manager.initialize_database()
        
        # اختبار إدراج عدد كبير من السجلات
        start_time = time.time()
        
        for i in range(1000):
            db_manager.log_activity(
                user_id=1,
                action=f'TEST_ACTION_{i}',
                description=f'Test activity {i}',
                severity='INFO'
            )
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"✅ إدراج 1000 سجل في {duration:.2f} ثانية")
        print(f"📊 معدل الإدراج: {1000/duration:.0f} سجل/ثانية")
        
        # اختبار الاستعلامات
        start_time = time.time()
        
        for i in range(100):
            results = db_manager.execute_query(
                "SELECT * FROM activity_logs WHERE action LIKE ?",
                (f'TEST_ACTION_{i}%',)
            )
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"✅ تنفيذ 100 استعلام في {duration:.2f} ثانية")
        print(f"📊 معدل الاستعلام: {100/duration:.0f} استعلام/ثانية")
        
    finally:
        try:
            os.unlink(test_db.name)
        except:
            pass

def run_stress_tests():
    """اختبارات الضغط"""
    print("💪 تشغيل اختبارات الضغط...")
    
    # اختبار تحمل النظام للعمليات المتزامنة
    def worker_thread(thread_id):
        """خيط عمل للاختبار"""
        try:
            # محاكاة عمليات مختلفة
            for i in range(50):
                # محاكاة عملية مصادقة
                auth_manager = AuthenticationManager()
                
                # محاكاة عملية تنبيه
                notification_manager = NotificationManager()
                notification_manager.send_notification(
                    'STRESS_TEST',
                    f'Stress Test {thread_id}-{i}',
                    f'Thread {thread_id} iteration {i}',
                    'INFO'
                )
                
                time.sleep(0.01)  # توقف قصير
                
        except Exception as e:
            print(f"❌ خطأ في الخيط {thread_id}: {e}")
    
    # تشغيل عدة خيوط متزامنة
    threads = []
    start_time = time.time()
    
    for i in range(10):
        thread = threading.Thread(target=worker_thread, args=(i,))
        threads.append(thread)
        thread.start()
    
    # انتظار انتهاء جميع الخيوط
    for thread in threads:
        thread.join()
    
    end_time = time.time()
    duration = end_time - start_time
    
    print(f"✅ تم تشغيل 10 خيوط متزامنة في {duration:.2f} ثانية")
    print(f"📊 إجمالي العمليات: 500 عملية")

def main():
    """الدالة الرئيسية للاختبارات"""
    print("🧪 بدء اختبارات Cyber Shield")
    print("=" * 50)
    
    # تشغيل اختبارات الوحدة
    print("🔍 تشغيل اختبارات الوحدة...")
    
    # إنشاء مجموعة الاختبارات
    test_suite = unittest.TestSuite()
    
    # إضافة اختبارات قاعدة البيانات
    test_suite.addTest(unittest.makeSuite(TestDatabaseManager))
    
    # إضافة اختبارات كلمات المرور
    test_suite.addTest(unittest.makeSuite(TestPasswordManager))
    
    # إضافة اختبارات المصادقة
    test_suite.addTest(unittest.makeSuite(TestAuthenticationManager))
    
    # إضافة اختبارات الأمان
    test_suite.addTest(unittest.makeSuite(TestSecurityManager))
    
    # إضافة اختبارات التنبيهات
    test_suite.addTest(unittest.makeSuite(TestNotificationManager))
    
    # إضافة اختبارات التكامل
    test_suite.addTest(unittest.makeSuite(TestIntegration))
    
    # تشغيل الاختبارات
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    print("\n" + "=" * 50)
    
    if result.wasSuccessful():
        print("✅ جميع اختبارات الوحدة نجحت!")
        
        # تشغيل اختبارات الأداء
        print("\n" + "=" * 50)
        run_performance_tests()
        
        # تشغيل اختبارات الضغط
        print("\n" + "=" * 50)
        run_stress_tests()
        
        print("\n" + "=" * 50)
        print("🎉 جميع الاختبارات نجحت بامتياز!")
        print("✅ التطبيق جاهز للإنتاج")
        
        return True
    else:
        print("❌ بعض الاختبارات فشلت!")
        print(f"الفشل: {len(result.failures)}")
        print(f"الأخطاء: {len(result.errors)}")
        
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
