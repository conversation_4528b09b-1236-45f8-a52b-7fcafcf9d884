<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - Cyber Shield</title>
    
    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #000000 0%, #1a5f1a 50%, #ffffff 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .login-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            overflow: hidden;
            max-width: 900px;
            width: 100%;
            margin: 20px;
        }
        
        .login-header {
            background: linear-gradient(135deg, #000000, #1a5f1a);
            color: white;
            padding: 40px;
            text-align: center;
        }
        
        .login-form {
            padding: 40px;
        }
        
        .form-control {
            border-radius: 10px;
            border: 2px solid #e0e0e0;
            padding: 15px;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            border-color: #1a5f1a;
            box-shadow: 0 0 0 0.2rem rgba(26, 95, 26, 0.25);
        }
        
        .btn-login {
            background: linear-gradient(135deg, #1a5f1a, #2d8f2d);
            border: none;
            border-radius: 10px;
            padding: 15px 30px;
            font-size: 18px;
            font-weight: 600;
            color: white;
            width: 100%;
            transition: all 0.3s ease;
        }
        
        .btn-login:hover {
            background: linear-gradient(135deg, #2d8f2d, #1a5f1a);
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(26, 95, 26, 0.3);
        }
        
        .flag-animation {
            animation: wave 2s ease-in-out infinite;
        }
        
        @keyframes wave {
            0%, 100% { transform: rotate(0deg); }
            25% { transform: rotate(5deg); }
            75% { transform: rotate(-5deg); }
        }
        
        .feature-card {
            background: rgba(26, 95, 26, 0.1);
            border-radius: 10px;
            padding: 20px;
            margin: 10px 0;
            border-right: 4px solid #1a5f1a;
        }
        
        .alert {
            border-radius: 10px;
            border: none;
        }
        
        .spinner-border-sm {
            width: 1rem;
            height: 1rem;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="row g-0">
            <!-- Left Side - Login Form -->
            <div class="col-lg-6">
                <div class="login-form">
                    <div class="text-center mb-4">
                        <i class="fas fa-shield-alt text-success" style="font-size: 3rem;"></i>
                        <h2 class="mt-3 mb-1">تسجيل الدخول</h2>
                        <p class="text-muted">ادخل إلى نظام الحماية السيبرانية</p>
                    </div>
                    
                    <!-- Alert Container -->
                    <div id="alert-container"></div>
                    
                    <!-- Login Form -->
                    <form id="loginForm">
                        <div class="mb-3">
                            <label for="username" class="form-label">
                                <i class="fas fa-user me-2"></i>اسم المستخدم
                            </label>
                            <input type="text" class="form-control" id="username" name="username" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="password" class="form-label">
                                <i class="fas fa-lock me-2"></i>كلمة المرور
                            </label>
                            <div class="input-group">
                                <input type="password" class="form-control" id="password" name="password" required>
                                <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </div>
                        
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="rememberMe">
                            <label class="form-check-label" for="rememberMe">
                                تذكرني
                            </label>
                        </div>
                        
                        <button type="submit" class="btn btn-login" id="loginBtn">
                            <span class="login-text">
                                <i class="fas fa-sign-in-alt me-2"></i>تسجيل الدخول
                            </span>
                            <span class="login-spinner d-none">
                                <span class="spinner-border spinner-border-sm me-2"></span>
                                جاري تسجيل الدخول...
                            </span>
                        </button>
                    </form>
                    
                    <hr class="my-4">
                    
                    <div class="text-center">
                        <small class="text-muted">
                            بيانات الدخول الافتراضية:<br>
                            <strong>المستخدم:</strong> admin | <strong>كلمة المرور:</strong> admin123
                        </small>
                    </div>
                </div>
            </div>
            
            <!-- Right Side - Info -->
            <div class="col-lg-6">
                <div class="login-header">
                    <div class="flag-animation mb-3">
                        <span style="font-size: 4rem;">🇵🇸</span>
                    </div>
                    <h1 class="mb-3">
                        <i class="fas fa-shield-alt me-2"></i>
                        Cyber Shield
                    </h1>
                    <h4 class="mb-4">Cyper Member | State of Palestine</h4>
                    <p class="lead">نظام حماية سيبرانية شامل مطور في فلسطين</p>
                    
                    <div class="mt-4">
                        <div class="feature-card">
                            <i class="fas fa-shield-virus text-success me-2"></i>
                            <strong>حماية متقدمة</strong>
                            <p class="mb-0 small">كشف التهديدات ومراقبة النظام</p>
                        </div>
                        
                        <div class="feature-card">
                            <i class="fas fa-map-marked-alt text-success me-2"></i>
                            <strong>تتبع المواقع</strong>
                            <p class="mb-0 small">خرائط تفاعلية للتهديدات</p>
                        </div>
                        
                        <div class="feature-card">
                            <i class="fas fa-bell text-success me-2"></i>
                            <strong>تنبيهات فورية</strong>
                            <p class="mb-0 small">إشعارات لحظية للأحداث المهمة</p>
                        </div>
                        
                        <div class="feature-card">
                            <i class="fas fa-cogs text-success me-2"></i>
                            <strong>إدارة متقدمة</strong>
                            <p class="mb-0 small">لوحة تحكم شاملة للمديرين</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // تبديل إظهار كلمة المرور
        document.getElementById('togglePassword').addEventListener('click', function() {
            const passwordField = document.getElementById('password');
            const icon = this.querySelector('i');
            
            if (passwordField.type === 'password') {
                passwordField.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                passwordField.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        });
        
        // معالج تسجيل الدخول
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const loginBtn = document.getElementById('loginBtn');
            const loginText = loginBtn.querySelector('.login-text');
            const loginSpinner = loginBtn.querySelector('.login-spinner');
            const alertContainer = document.getElementById('alert-container');
            
            // إظهار حالة التحميل
            loginText.classList.add('d-none');
            loginSpinner.classList.remove('d-none');
            loginBtn.disabled = true;
            
            // مسح التنبيهات السابقة
            alertContainer.innerHTML = '';
            
            try {
                const formData = new FormData(this);
                const data = {
                    username: formData.get('username'),
                    password: formData.get('password')
                };
                
                const response = await fetch('/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });
                
                const result = await response.json();
                
                if (result.success) {
                    // نجح تسجيل الدخول
                    showAlert('تم تسجيل الدخول بنجاح! جاري التوجيه...', 'success');
                    
                    setTimeout(() => {
                        window.location.href = result.redirect;
                    }, 1000);
                } else {
                    // فشل تسجيل الدخول
                    showAlert(result.message, 'danger');
                }
                
            } catch (error) {
                console.error('خطأ في تسجيل الدخول:', error);
                showAlert('حدث خطأ في الاتصال بالخادم', 'danger');
            } finally {
                // إخفاء حالة التحميل
                loginText.classList.remove('d-none');
                loginSpinner.classList.add('d-none');
                loginBtn.disabled = false;
            }
        });
        
        function showAlert(message, type) {
            const alertContainer = document.getElementById('alert-container');
            const alertHtml = `
                <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                    <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-triangle'} me-2"></i>
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            alertContainer.innerHTML = alertHtml;
        }
        
        // تركيز على حقل اسم المستخدم عند تحميل الصفحة
        window.addEventListener('load', function() {
            document.getElementById('username').focus();
        });
        
        // معالج Enter في الحقول
        document.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                const form = document.getElementById('loginForm');
                if (form.checkValidity()) {
                    form.dispatchEvent(new Event('submit'));
                }
            }
        });
    </script>
</body>
</html>
