# 🛡️ Cyber Shield - Cyper Member | State of Palestine

<div align="center">

![Palestinian Flag](https://upload.wikimedia.org/wikipedia/commons/thumb/0/00/Flag_of_Palestine.svg/100px-Flag_of_Palestine.svg.png)

**نظام حماية سيبرانية شامل من فلسطين**

[![Version](https://img.shields.io/badge/version-1.0.0-blue.svg)](https://github.com/cyper-member/cyber-shield)
[![License](https://img.shields.io/badge/license-MIT-green.svg)](LICENSE)
[![Python](https://img.shields.io/badge/python-3.8+-blue.svg)](https://python.org)
[![Platform](https://img.shields.io/badge/platform-Windows-lightgrey.svg)](https://www.microsoft.com/windows)

</div>

## 📋 نظرة عامة

**Cyber Shield** هو نظام حماية سيبرانية شامل مطور بواسطة **Cyper Member** من **دولة فلسطين**. يوفر التطبيق حماية متقدمة ضد التهديدات السيبرانية مع واجهة مستخدم عربية سهلة الاستخدام.

## ✨ الميزات الرئيسية

### 🔐 نظام المصادقة والأمان
- تسجيل دخول آمن مع تشفير كلمات المرور
- التحقق بخطوتين (2FA)
- إدارة الجلسات المتقدمة
- نظام أذونات متدرج

### 🛡️ الحماية السيبرانية
- **مراقبة الملفات**: رصد التغييرات في ملفات النظام
- **كشف التهديدات**: اكتشاف البرمجيات الخبيثة والفيروسات
- **مراقبة الشبكة**: تتبع الاتصالات المشبوهة
- **الحجر الصحي**: عزل الملفات المشبوهة تلقائياً

### 🌍 تتبع المواقع الجغرافية
- تحديد مواقع عناوين IP
- خرائط تفاعلية للاتصالات
- إحصائيات جغرافية مفصلة
- تتبع التهديدات حسب الموقع

### 👨‍💼 لوحة تحكم المسؤول
- إدارة المستخدمين
- عرض السجلات والتقارير
- إحصائيات النظام
- إعدادات الأمان المتقدمة

### 🔔 نظام التنبيهات
- إشعارات سطح المكتب
- تنبيهات البريد الإلكتروني
- تنبيهات صوتية للتهديدات الحرجة
- سجل شامل للأحداث

### 🔄 التحديث التلقائي
- فحص التحديثات تلقائياً
- تحميل وتثبيت التحديثات
- نسخ احتياطية تلقائية
- استعادة في حالة الفشل

## 🚀 التثبيت والتشغيل

### متطلبات النظام
- **نظام التشغيل**: Windows 10/11
- **Python**: 3.8 أو أحدث
- **الذاكرة**: 4 GB RAM (الحد الأدنى)
- **التخزين**: 500 MB مساحة فارغة

### التثبيت من المصدر

1. **استنساخ المستودع**:
```bash
git clone https://github.com/cyper-member/cyber-shield.git
cd cyber-shield
```

2. **تثبيت المتطلبات**:
```bash
pip install -r requirements.txt
```

3. **تشغيل التطبيق**:
```bash
python main.py
```

### بناء ملف EXE

لبناء ملف تنفيذي مستقل:

```bash
python build_exe.py
```

سيتم إنشاء ملف EXE في مجلد `dist/`.

## 🎯 كيفية الاستخدام

### البدء السريع

1. **تشغيل التطبيق** وإنشاء حساب مدير
2. **تفعيل أنظمة الحماية** من لوحة التحكم
3. **تخصيص إعدادات الأمان** حسب احتياجاتك
4. **مراقبة التهديدات** من خلال لوحة المعلومات

### الحساب الافتراضي

- **اسم المستخدم**: `admin`
- **كلمة المرور**: `admin123`

⚠️ **تأكد من تغيير كلمة المرور فور تسجيل الدخول الأول**

## 📊 لقطات الشاشة

### نافذة تسجيل الدخول
![Login Window](assets/screenshots/login.png)

### لوحة التحكم الرئيسية
![Dashboard](assets/screenshots/dashboard.png)

### خريطة التهديدات
![Threats Map](assets/screenshots/threats_map.png)

## 🏗️ البنية التقنية

```
cyber-shield/
├── main.py                 # نقطة الدخول الرئيسية
├── config/                 # ملفات التكوين
│   └── settings.py
├── src/                    # الكود المصدري
│   ├── auth/              # نظام المصادقة
│   ├── database/          # إدارة قاعدة البيانات
│   ├── gui/               # الواجهة الرسومية
│   ├── security/          # أنظمة الحماية
│   ├── admin/             # لوحة الإدارة
│   └── utils/             # أدوات مساعدة
├── database/              # قاعدة البيانات
├── assets/                # الموارد والصور
├── logs/                  # ملفات السجلات
└── requirements.txt       # المتطلبات
```

## 🔧 التكوين المتقدم

### إعدادات قاعدة البيانات
```python
DATABASE_PATH = Path("database/cyber_shield.db")
```

### إعدادات البريد الإلكتروني
```python
EMAIL_SENDER = "<EMAIL>"
EMAIL_PASSWORD = "your-app-password"
SMTP_SERVER = "smtp.gmail.com"
SMTP_PORT = 587
```

### إعدادات الأمان
```python
SESSION_TIMEOUT = 3600  # ثانية
MAX_LOGIN_ATTEMPTS = 5
PASSWORD_MIN_LENGTH = 8
```

## 🤝 المساهمة

نرحب بمساهماتكم! يرجى اتباع الخطوات التالية:

1. Fork المستودع
2. إنشاء فرع للميزة الجديدة (`git checkout -b feature/amazing-feature`)
3. Commit التغييرات (`git commit -m 'Add amazing feature'`)
4. Push للفرع (`git push origin feature/amazing-feature`)
5. فتح Pull Request

## 📝 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 🆘 الدعم والمساعدة

### الإبلاغ عن المشاكل
- [GitHub Issues](https://github.com/cyper-member/cyber-shield/issues)

### التوثيق
- [Wiki](https://github.com/cyper-member/cyber-shield/wiki)

### التواصل
- **البريد الإلكتروني**: <EMAIL>
- **Telegram**: @CyperMemberPalestine

## 🏆 الشكر والتقدير

- **فريق Cyper Member** - التطوير والتصميم
- **المجتمع الفلسطيني التقني** - الدعم والمراجعة
- **مطوري المكتبات مفتوحة المصدر** - الأدوات والمكتبات

## 🇵🇸 رسالة من فلسطين

هذا المشروع مطور بكل فخر من **دولة فلسطين** كمساهمة في الأمن السيبراني العربي والعالمي. نؤمن بأن التكنولوجيا يجب أن تكون في خدمة الإنسانية وحماية الحقوق الرقمية للجميع.

**🇵🇸 فلسطين حرة - من النهر إلى البحر 🇵🇸**

---

<div align="center">

**صنع بـ ❤️ في فلسطين**

[![Palestinian Flag](https://upload.wikimedia.org/wikipedia/commons/thumb/0/00/Flag_of_Palestine.svg/50px-Flag_of_Palestine.svg.png)](https://en.wikipedia.org/wiki/Palestine)

**Cyper Member | State of Palestine**

</div>
