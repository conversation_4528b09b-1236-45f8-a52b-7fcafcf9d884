# -*- coding: utf-8 -*-
"""
Cyber Shield - File Monitor
مراقب الملفات والتغييرات
"""

import os
import time
import hashlib
import logging
import threading
from pathlib import Path
from typing import Dict, List, Optional, Callable
from datetime import datetime
import json

from src.database.db_manager import DatabaseManager
from src.auth.password_manager import PasswordManager
from config.settings import *

logger = logging.getLogger(__name__)

class FileMonitor:
    """
    مراقب الملفات والتغييرات
    """
    
    def __init__(self):
        self.db_manager = DatabaseManager()
        self.password_manager = PasswordManager()
        self.is_monitoring = False
        self.monitor_thread = None
        self.watched_paths = set()
        self.file_hashes = {}
        self.callbacks = []
        
        # مسارات النظام الحساسة
        self.critical_paths = {
            "C:\\Windows\\System32",
            "C:\\Windows\\SysWOW64", 
            "C:\\Program Files",
            "C:\\Program Files (x86)",
            os.path.expanduser("~\\AppData\\Roaming\\Microsoft\\Windows\\Start Menu"),
            os.path.expanduser("~\\Documents"),
            os.path.expanduser("~\\Desktop")
        }
        
        # أنواع الملفات المشبوهة
        self.suspicious_extensions = {
            '.exe', '.bat', '.cmd', '.com', '.scr', '.pif',
            '.vbs', '.js', '.jar', '.ps1', '.reg', '.msi'
        }
        
        # إضافة المسارات الحساسة للمراقبة
        self.watched_paths.update(self.critical_paths)
    
    def start_monitoring(self):
        """
        بدء مراقبة الملفات
        """
        if self.is_monitoring:
            logger.warning("⚠️ مراقبة الملفات قيد التشغيل بالفعل")
            return
        
        self.is_monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
        
        logger.info("🔍 تم بدء مراقبة الملفات")
        self._log_activity("FILE_MONITORING_STARTED", "تم بدء مراقبة الملفات")
    
    def stop_monitoring(self):
        """
        إيقاف مراقبة الملفات
        """
        self.is_monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
        
        logger.info("🛑 تم إيقاف مراقبة الملفات")
        self._log_activity("FILE_MONITORING_STOPPED", "تم إيقاف مراقبة الملفات")
    
    def add_watched_path(self, path: str):
        """
        إضافة مسار للمراقبة
        """
        if os.path.exists(path):
            self.watched_paths.add(path)
            logger.info(f"📁 تم إضافة مسار للمراقبة: {path}")
        else:
            logger.warning(f"⚠️ المسار غير موجود: {path}")
    
    def remove_watched_path(self, path: str):
        """
        إزالة مسار من المراقبة
        """
        self.watched_paths.discard(path)
        logger.info(f"📁 تم إزالة مسار من المراقبة: {path}")
    
    def add_callback(self, callback: Callable):
        """
        إضافة دالة استدعاء عند اكتشاف تغيير
        """
        self.callbacks.append(callback)
    
    def _monitor_loop(self):
        """
        حلقة المراقبة الرئيسية
        """
        # بناء فهرس أولي للملفات
        self._build_initial_index()
        
        while self.is_monitoring:
            try:
                self._scan_for_changes()
                time.sleep(5)  # فحص كل 5 ثوانٍ
                
            except Exception as e:
                logger.error(f"❌ خطأ في مراقبة الملفات: {e}")
                time.sleep(10)  # انتظار أطول في حالة الخطأ
    
    def _build_initial_index(self):
        """
        بناء فهرس أولي للملفات
        """
        logger.info("📋 بناء فهرس الملفات الأولي...")
        
        for path in self.watched_paths:
            if os.path.exists(path):
                self._index_directory(path)
        
        logger.info(f"✅ تم فهرسة {len(self.file_hashes)} ملف")
    
    def _index_directory(self, directory: str):
        """
        فهرسة مجلد
        """
        try:
            for root, dirs, files in os.walk(directory):
                # تجاهل المجلدات المخفية والنظام
                dirs[:] = [d for d in dirs if not d.startswith('.') and d not in ['$RECYCLE.BIN', 'System Volume Information']]
                
                for file in files:
                    file_path = os.path.join(root, file)
                    try:
                        if os.path.isfile(file_path):
                            file_hash = self._calculate_file_hash(file_path)
                            if file_hash:
                                self.file_hashes[file_path] = {
                                    'hash': file_hash,
                                    'size': os.path.getsize(file_path),
                                    'modified': os.path.getmtime(file_path)
                                }
                    except (OSError, PermissionError):
                        continue  # تجاهل الملفات التي لا يمكن الوصول إليها
                        
        except (OSError, PermissionError) as e:
            logger.warning(f"⚠️ لا يمكن الوصول للمجلد {directory}: {e}")
    
    def _scan_for_changes(self):
        """
        فحص التغييرات في الملفات
        """
        current_files = {}
        
        # فحص الملفات الحالية
        for path in self.watched_paths:
            if os.path.exists(path):
                self._scan_directory(path, current_files)
        
        # مقارنة مع الفهرس السابق
        self._compare_file_states(current_files)
        
        # تحديث الفهرس
        self.file_hashes = current_files
    
    def _scan_directory(self, directory: str, current_files: Dict):
        """
        فحص مجلد للتغييرات
        """
        try:
            for root, dirs, files in os.walk(directory):
                dirs[:] = [d for d in dirs if not d.startswith('.') and d not in ['$RECYCLE.BIN', 'System Volume Information']]
                
                for file in files:
                    file_path = os.path.join(root, file)
                    try:
                        if os.path.isfile(file_path):
                            file_hash = self._calculate_file_hash(file_path)
                            if file_hash:
                                current_files[file_path] = {
                                    'hash': file_hash,
                                    'size': os.path.getsize(file_path),
                                    'modified': os.path.getmtime(file_path)
                                }
                    except (OSError, PermissionError):
                        continue
                        
        except (OSError, PermissionError):
            pass
    
    def _compare_file_states(self, current_files: Dict):
        """
        مقارنة حالة الملفات الحالية مع السابقة
        """
        # ملفات جديدة
        new_files = set(current_files.keys()) - set(self.file_hashes.keys())
        for file_path in new_files:
            self._handle_file_created(file_path, current_files[file_path])
        
        # ملفات محذوفة
        deleted_files = set(self.file_hashes.keys()) - set(current_files.keys())
        for file_path in deleted_files:
            self._handle_file_deleted(file_path)
        
        # ملفات معدلة
        for file_path in set(current_files.keys()) & set(self.file_hashes.keys()):
            old_info = self.file_hashes[file_path]
            new_info = current_files[file_path]
            
            if old_info['hash'] != new_info['hash']:
                self._handle_file_modified(file_path, old_info, new_info)
    
    def _handle_file_created(self, file_path: str, file_info: Dict):
        """
        معالجة إنشاء ملف جديد
        """
        is_suspicious = self._is_suspicious_file(file_path)
        
        # تسجيل في قاعدة البيانات
        self._log_file_event(file_path, 'CREATED', is_suspicious, file_info)
        
        # إشعار المستمعين
        self._notify_callbacks('file_created', file_path, is_suspicious)
        
        if is_suspicious:
            logger.warning(f"⚠️ ملف مشبوه تم إنشاؤه: {file_path}")
        else:
            logger.info(f"📄 ملف جديد: {file_path}")
    
    def _handle_file_deleted(self, file_path: str):
        """
        معالجة حذف ملف
        """
        is_critical = self._is_critical_file(file_path)
        
        # تسجيل في قاعدة البيانات
        self._log_file_event(file_path, 'DELETED', is_critical)
        
        # إشعار المستمعين
        self._notify_callbacks('file_deleted', file_path, is_critical)
        
        if is_critical:
            logger.error(f"🚨 ملف نظام حساس تم حذفه: {file_path}")
        else:
            logger.info(f"🗑️ ملف محذوف: {file_path}")
    
    def _handle_file_modified(self, file_path: str, old_info: Dict, new_info: Dict):
        """
        معالجة تعديل ملف
        """
        is_critical = self._is_critical_file(file_path)
        
        # تسجيل في قاعدة البيانات
        self._log_file_event(file_path, 'MODIFIED', is_critical, new_info, old_info)
        
        # إشعار المستمعين
        self._notify_callbacks('file_modified', file_path, is_critical)
        
        if is_critical:
            logger.warning(f"⚠️ ملف نظام حساس تم تعديله: {file_path}")
        else:
            logger.info(f"✏️ ملف معدل: {file_path}")
    
    def _is_suspicious_file(self, file_path: str) -> bool:
        """
        التحقق من كون الملف مشبوه
        """
        file_ext = Path(file_path).suffix.lower()
        file_name = Path(file_path).name.lower()
        
        # فحص الامتداد
        if file_ext in self.suspicious_extensions:
            return True
        
        # فحص أسماء مشبوهة
        suspicious_names = ['virus', 'malware', 'trojan', 'keylogger', 'backdoor']
        if any(name in file_name for name in suspicious_names):
            return True
        
        # فحص المسار
        temp_paths = ['temp', 'tmp', 'appdata\\local\\temp']
        if any(temp_path in file_path.lower() for temp_path in temp_paths):
            if file_ext in self.suspicious_extensions:
                return True
        
        return False
    
    def _is_critical_file(self, file_path: str) -> bool:
        """
        التحقق من كون الملف حساس
        """
        critical_dirs = ['system32', 'syswow64', 'windows']
        return any(critical_dir in file_path.lower() for critical_dir in critical_dirs)
    
    def _calculate_file_hash(self, file_path: str) -> Optional[str]:
        """
        حساب hash للملف
        """
        try:
            return self.password_manager.hash_file(file_path)
        except Exception as e:
            logger.debug(f"لا يمكن حساب hash للملف {file_path}: {e}")
            return None
    
    def _log_file_event(self, file_path: str, action: str, is_suspicious: bool, 
                       new_info: Dict = None, old_info: Dict = None):
        """
        تسجيل حدث الملف في قاعدة البيانات
        """
        try:
            additional_info = {
                'file_size': new_info.get('size') if new_info else None,
                'file_hash': new_info.get('hash') if new_info else None,
                'old_hash': old_info.get('hash') if old_info else None,
                'modified_time': new_info.get('modified') if new_info else None
            }
            
            query = """
                INSERT INTO file_monitoring (
                    file_path, file_hash, file_size, action, 
                    detected_at, is_suspicious
                ) VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP, ?)
            """
            
            self.db_manager.execute_query(query, (
                file_path,
                new_info.get('hash') if new_info else None,
                new_info.get('size') if new_info else None,
                action,
                is_suspicious
            ))
            
        except Exception as e:
            logger.error(f"❌ خطأ في تسجيل حدث الملف: {e}")
    
    def _log_activity(self, action: str, description: str):
        """
        تسجيل نشاط في السجل
        """
        try:
            self.db_manager.log_activity(
                user_id=None,
                action=action,
                description=description,
                severity='INFO'
            )
        except Exception as e:
            logger.error(f"❌ خطأ في تسجيل النشاط: {e}")
    
    def _notify_callbacks(self, event_type: str, file_path: str, is_critical: bool):
        """
        إشعار المستمعين بالأحداث
        """
        for callback in self.callbacks:
            try:
                callback(event_type, file_path, is_critical)
            except Exception as e:
                logger.error(f"❌ خطأ في استدعاء callback: {e}")
    
    def get_recent_events(self, limit: int = 100) -> List[Dict]:
        """
        الحصول على الأحداث الأخيرة
        """
        try:
            query = """
                SELECT * FROM file_monitoring 
                ORDER BY detected_at DESC 
                LIMIT ?
            """
            return self.db_manager.execute_query(query, (limit,))
        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على الأحداث: {e}")
            return []
    
    def get_suspicious_files(self) -> List[Dict]:
        """
        الحصول على الملفات المشبوهة
        """
        try:
            query = """
                SELECT * FROM file_monitoring 
                WHERE is_suspicious = TRUE 
                ORDER BY detected_at DESC
            """
            return self.db_manager.execute_query(query)
        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على الملفات المشبوهة: {e}")
            return []
