# -*- coding: utf-8 -*-
"""
Cyber Shield - Web Application
تطبيق الويب لـ Cyber Shield
"""

import sys
import os
import json
import logging
from pathlib import Path
from datetime import datetime, timedelta
from functools import wraps

from flask import Flask, render_template, request, jsonify, session, redirect, url_for, flash, send_file
from flask_socketio import SocketIO, emit, join_room, leave_room
import secrets

# إضافة مسار المشروع
sys.path.insert(0, str(Path(__file__).parent))

from config.settings import *
from src.database.db_manager import DatabaseManager
from src.auth.auth_manager import AuthenticationManager
from src.auth.session_manager import SessionManager
from src.security.security_manager import SecurityManager
from src.admin.admin_panel import AdminPanel
from src.utils.notification_manager import NotificationManager
from src.utils.location_tracker import LocationTracker
from src.utils.map_generator import MapGenerator

# إعداد Flask
app = Flask(__name__)
app.secret_key = secrets.token_hex(32)
app.config['PERMANENT_SESSION_LIFETIME'] = timedelta(hours=24)

# إعداد SocketIO للتحديثات المباشرة
socketio = SocketIO(app, cors_allowed_origins="*")

# إعداد السجلات
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# تهيئة المكونات
db_manager = DatabaseManager()
auth_manager = AuthenticationManager()
session_manager = SessionManager()
security_manager = SecurityManager()
admin_panel = AdminPanel()
notification_manager = NotificationManager()
location_tracker = LocationTracker()
map_generator = MapGenerator()

def init_app():
    """تهيئة التطبيق"""
    try:
        # إنشاء المجلدات المطلوبة
        for directory in [DATA_DIR, TEMP_DIR, LOGS_DIR, ASSETS_DIR]:
            directory.mkdir(exist_ok=True)
        
        # تهيئة قاعدة البيانات
        db_manager.initialize_database()
        
        logger.info("✅ تم تهيئة تطبيق الويب بنجاح")
        return True
        
    except Exception as e:
        logger.error(f"❌ خطأ في تهيئة التطبيق: {e}")
        return False

def login_required(f):
    """ديكوريتر للتحقق من تسجيل الدخول"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            return redirect(url_for('login'))
        
        # التحقق من صحة الجلسة
        user_data = session_manager.validate_session(session.get('session_token'))
        if not user_data:
            session.clear()
            return redirect(url_for('login'))
        
        return f(*args, **kwargs)
    return decorated_function

def admin_required(f):
    """ديكوريتر للتحقق من صلاحيات المدير"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            return redirect(url_for('login'))
        
        user_data = session_manager.validate_session(session.get('session_token'))
        if not user_data or not user_data.get('is_admin'):
            flash('ليس لديك صلاحية للوصول لهذه الصفحة', 'error')
            return redirect(url_for('dashboard'))
        
        return f(*args, **kwargs)
    return decorated_function

@app.route('/')
def index():
    """الصفحة الرئيسية"""
    if 'user_id' in session:
        return redirect(url_for('dashboard'))
    return redirect(url_for('login'))

@app.route('/login', methods=['GET', 'POST'])
def login():
    """صفحة تسجيل الدخول"""
    if request.method == 'POST':
        try:
            data = request.get_json() if request.is_json else request.form
            username = data.get('username')
            password = data.get('password')
            
            if not username or not password:
                return jsonify({'success': False, 'message': 'يرجى إدخال اسم المستخدم وكلمة المرور'})
            
            # محاولة تسجيل الدخول
            success, message, user_data = auth_manager.login_user(
                username, password, request.remote_addr
            )
            
            if success:
                # إنشاء جلسة
                session['user_id'] = user_data['id']
                session['username'] = user_data['username']
                session['is_admin'] = user_data.get('is_admin', False)
                session['session_token'] = user_data['session_token']
                session.permanent = True
                
                return jsonify({
                    'success': True, 
                    'message': 'تم تسجيل الدخول بنجاح',
                    'redirect': url_for('dashboard')
                })
            else:
                return jsonify({'success': False, 'message': message})
                
        except Exception as e:
            logger.error(f"خطأ في تسجيل الدخول: {e}")
            return jsonify({'success': False, 'message': 'حدث خطأ في تسجيل الدخول'})
    
    return render_template('login.html')

@app.route('/logout')
@login_required
def logout():
    """تسجيل الخروج"""
    try:
        # إلغاء الجلسة
        if 'session_token' in session:
            session_manager.invalidate_session(session['session_token'])
        
        session.clear()
        flash('تم تسجيل الخروج بنجاح', 'success')
        
    except Exception as e:
        logger.error(f"خطأ في تسجيل الخروج: {e}")
    
    return redirect(url_for('login'))

@app.route('/dashboard')
@login_required
def dashboard():
    """لوحة المعلومات الرئيسية"""
    try:
        # الحصول على بيانات المستخدم
        user_data = session_manager.validate_session(session.get('session_token'))
        
        # الحصول على بيانات لوحة المعلومات
        dashboard_data = admin_panel.get_dashboard_data(user_data)
        
        return render_template('dashboard.html', 
                             user=user_data, 
                             dashboard=dashboard_data)
        
    except Exception as e:
        logger.error(f"خطأ في لوحة المعلومات: {e}")
        flash('حدث خطأ في تحميل لوحة المعلومات', 'error')
        return redirect(url_for('login'))

@app.route('/security')
@login_required
def security():
    """صفحة الأمان"""
    try:
        user_data = session_manager.validate_session(session.get('session_token'))
        security_data = security_manager.get_security_dashboard()
        
        return render_template('security.html', 
                             user=user_data, 
                             security=security_data)
        
    except Exception as e:
        logger.error(f"خطأ في صفحة الأمان: {e}")
        flash('حدث خطأ في تحميل صفحة الأمان', 'error')
        return redirect(url_for('dashboard'))

@app.route('/location')
@login_required
def location():
    """صفحة تتبع المواقع"""
    try:
        user_data = session_manager.validate_session(session.get('session_token'))
        
        # الحصول على إحصائيات المواقع
        location_stats = location_tracker.get_ip_statistics()
        
        return render_template('location.html', 
                             user=user_data, 
                             stats=location_stats)
        
    except Exception as e:
        logger.error(f"خطأ في صفحة المواقع: {e}")
        flash('حدث خطأ في تحميل صفحة المواقع', 'error')
        return redirect(url_for('dashboard'))

@app.route('/admin')
@admin_required
def admin():
    """لوحة الإدارة"""
    try:
        user_data = session_manager.validate_session(session.get('session_token'))
        admin_stats = admin_panel.get_admin_statistics()
        
        return render_template('admin.html', 
                             user=user_data, 
                             stats=admin_stats)
        
    except Exception as e:
        logger.error(f"خطأ في لوحة الإدارة: {e}")
        flash('حدث خطأ في تحميل لوحة الإدارة', 'error')
        return redirect(url_for('dashboard'))

# API Routes
@app.route('/api/security/status')
@login_required
def api_security_status():
    """API: حالة الأمان"""
    try:
        user_data = session_manager.validate_session(session.get('session_token'))
        success, message, data = admin_panel.manage_security(user_data, 'status')
        
        return jsonify({
            'success': success,
            'message': message,
            'data': data
        })
        
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/security/start', methods=['POST'])
@login_required
def api_security_start():
    """API: بدء الحماية"""
    try:
        user_data = session_manager.validate_session(session.get('session_token'))
        success, message, _ = admin_panel.manage_security(user_data, 'start_protection')
        
        # إرسال تحديث مباشر
        socketio.emit('security_update', {
            'type': 'protection_started',
            'message': message
        })
        
        return jsonify({'success': success, 'message': message})
        
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/security/stop', methods=['POST'])
@login_required
def api_security_stop():
    """API: إيقاف الحماية"""
    try:
        user_data = session_manager.validate_session(session.get('session_token'))
        success, message, _ = admin_panel.manage_security(user_data, 'stop_protection')
        
        # إرسال تحديث مباشر
        socketio.emit('security_update', {
            'type': 'protection_stopped',
            'message': message
        })
        
        return jsonify({'success': success, 'message': message})
        
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/scan/quick', methods=['POST'])
@login_required
def api_quick_scan():
    """API: فحص سريع"""
    try:
        user_data = session_manager.validate_session(session.get('session_token'))
        
        def scan_callback(progress):
            socketio.emit('scan_progress', {
                'type': 'quick_scan',
                'progress': progress
            })
        
        success, message, _ = admin_panel.manage_security(
            user_data, 'quick_scan', callback=scan_callback
        )
        
        return jsonify({'success': success, 'message': message})
        
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/location/current')
@login_required
def api_current_location():
    """API: الموقع الحالي"""
    try:
        user_data = session_manager.validate_session(session.get('session_token'))
        success, message, data = admin_panel.manage_location_tracking(
            user_data, 'current_location'
        )
        
        return jsonify({
            'success': success,
            'message': message,
            'data': data
        })
        
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/map/generate', methods=['POST'])
@login_required
def api_generate_map():
    """API: إنشاء خريطة"""
    try:
        user_data = session_manager.validate_session(session.get('session_token'))
        data = request.get_json()
        
        map_type = data.get('type', 'connections')
        limit = data.get('limit', 100)
        
        success, message, result = admin_panel.manage_location_tracking(
            user_data, 'generate_map', map_type=map_type, limit=limit
        )
        
        return jsonify({
            'success': success,
            'message': message,
            'data': result
        })
        
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/logs/<log_type>')
@login_required
def api_get_logs(log_type):
    """API: الحصول على السجلات"""
    try:
        user_data = session_manager.validate_session(session.get('session_token'))

        # الحصول على المعاملات
        limit = request.args.get('limit', 50, type=int)
        filters = {}

        # تطبيق الفلاتر
        if request.args.get('start_date'):
            filters['start_date'] = request.args.get('start_date')
        if request.args.get('end_date'):
            filters['end_date'] = request.args.get('end_date')
        if request.args.get('severity'):
            filters['severity'] = request.args.get('severity')
        if request.args.get('date'):
            filters['date'] = request.args.get('date')

        success, message, data = admin_panel.manage_logs(
            user_data, log_type, filters=filters, limit=limit
        )

        return jsonify({
            'success': success,
            'message': message,
            'data': data
        })

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

# Admin API Routes
@app.route('/api/admin/users')
@admin_required
def api_admin_users():
    """API: الحصول على قائمة المستخدمين"""
    try:
        user_data = session_manager.validate_session(session.get('session_token'))
        success, message, data = admin_panel.get_all_users(user_data)

        return jsonify({
            'success': success,
            'message': message,
            'data': data
        })

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/admin/users', methods=['POST'])
@admin_required
def api_admin_create_user():
    """API: إنشاء مستخدم جديد"""
    try:
        user_data = session_manager.validate_session(session.get('session_token'))
        data = request.get_json()

        success, message, user_id = admin_panel.create_user(user_data, data)

        return jsonify({
            'success': success,
            'message': message,
            'user_id': user_id
        })

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/admin/users/<int:user_id>/toggle', methods=['POST'])
@admin_required
def api_admin_toggle_user(user_id):
    """API: تفعيل/إلغاء تفعيل المستخدم"""
    try:
        user_data = session_manager.validate_session(session.get('session_token'))
        data = request.get_json()
        activate = data.get('activate', True)

        success, message = admin_panel.toggle_user_status(user_data, user_id, activate)

        return jsonify({
            'success': success,
            'message': message
        })

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/admin/users/<int:user_id>', methods=['DELETE'])
@admin_required
def api_admin_delete_user(user_id):
    """API: حذف المستخدم"""
    try:
        user_data = session_manager.validate_session(session.get('session_token'))
        success, message = admin_panel.delete_user(user_data, user_id)

        return jsonify({
            'success': success,
            'message': message
        })

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/admin/system-info')
@admin_required
def api_admin_system_info():
    """API: معلومات النظام"""
    try:
        system_info = auth_manager.get_system_info()

        return jsonify({
            'success': True,
            'data': system_info
        })

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/admin/network-info')
@admin_required
def api_admin_network_info():
    """API: معلومات الشبكة"""
    try:
        network_info = {
            'public_ip': auth_manager.get_public_ip(),
            'interfaces': auth_manager.get_network_interfaces(),
            'security': auth_manager.get_system_security_status()
        }

        return jsonify({
            'success': True,
            'data': network_info
        })

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/admin/backup', methods=['POST'])
@admin_required
def api_admin_create_backup():
    """API: إنشاء نسخة احتياطية"""
    try:
        user_data = session_manager.validate_session(session.get('session_token'))
        success, message, filename = admin_panel.create_backup(user_data)

        return jsonify({
            'success': success,
            'message': message,
            'filename': filename
        })

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/admin/backup/download/<filename>')
@admin_required
def api_admin_download_backup(filename):
    """API: تحميل النسخة الاحتياطية"""
    try:
        backup_path = TEMP_DIR / "backups" / filename
        if backup_path.exists():
            return send_file(backup_path, as_attachment=True)
        else:
            return jsonify({'success': False, 'message': 'الملف غير موجود'}), 404

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/admin/backup/restore', methods=['POST'])
@admin_required
def api_admin_restore_backup():
    """API: استعادة النسخة الاحتياطية"""
    try:
        user_data = session_manager.validate_session(session.get('session_token'))

        if 'backup_file' not in request.files:
            return jsonify({'success': False, 'message': 'لم يتم اختيار ملف'})

        file = request.files['backup_file']
        if file.filename == '':
            return jsonify({'success': False, 'message': 'لم يتم اختيار ملف'})

        success, message = admin_panel.restore_backup(user_data, file)

        return jsonify({
            'success': success,
            'message': message
        })

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/admin/settings/security', methods=['POST'])
@admin_required
def api_admin_save_security_settings():
    """API: حفظ إعدادات الأمان"""
    try:
        user_data = session_manager.validate_session(session.get('session_token'))
        settings = request.get_json()

        success, message = admin_panel.save_security_settings(user_data, settings)

        return jsonify({
            'success': success,
            'message': message
        })

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/admin/settings/email', methods=['POST'])
@admin_required
def api_admin_save_email_settings():
    """API: حفظ إعدادات البريد الإلكتروني"""
    try:
        user_data = session_manager.validate_session(session.get('session_token'))
        settings = request.get_json()

        success, message = admin_panel.save_email_settings(user_data, settings)

        return jsonify({
            'success': success,
            'message': message
        })

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

# Location API Routes
@app.route('/api/location/ip/<ip>')
@login_required
def api_location_ip(ip):
    """API: تحليل عنوان IP"""
    try:
        user_data = session_manager.validate_session(session.get('session_token'))
        success, message, data = admin_panel.analyze_ip_location(user_data, ip)

        return jsonify({
            'success': success,
            'message': message,
            'data': data
        })

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/location/history')
@login_required
def api_location_history():
    """API: تاريخ المواقع"""
    try:
        user_data = session_manager.validate_session(session.get('session_token'))
        limit = request.args.get('limit', 50, type=int)

        success, message, data = admin_panel.get_location_history(user_data, limit)

        return jsonify({
            'success': success,
            'message': message,
            'data': data
        })

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/location/countries-stats')
@login_required
def api_location_countries_stats():
    """API: إحصائيات الدول"""
    try:
        user_data = session_manager.validate_session(session.get('session_token'))
        success, message, data = admin_panel.get_countries_statistics(user_data)

        return jsonify({
            'success': success,
            'message': message,
            'data': data
        })

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

# Security API Routes
@app.route('/api/security/level', methods=['POST'])
@login_required
def api_security_level():
    """API: تعيين مستوى الأمان"""
    try:
        user_data = session_manager.validate_session(session.get('session_token'))
        data = request.get_json()
        level = data.get('level')

        success, message, _ = admin_panel.set_security_level(user_data, level)

        return jsonify({
            'success': success,
            'message': message
        })

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/security/update-definitions', methods=['POST'])
@login_required
def api_security_update_definitions():
    """API: تحديث قاعدة بيانات التهديدات"""
    try:
        user_data = session_manager.validate_session(session.get('session_token'))
        success, message, _ = admin_panel.update_threat_definitions(user_data)

        return jsonify({
            'success': success,
            'message': message
        })

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

# WebSocket Events
@socketio.on('connect')
def handle_connect():
    """اتصال WebSocket"""
    logger.info(f"عميل متصل: {request.sid}")

@socketio.on('disconnect')
def handle_disconnect():
    """قطع اتصال WebSocket"""
    logger.info(f"عميل منقطع: {request.sid}")

@socketio.on('join_room')
def handle_join_room(data):
    """الانضمام لغرفة"""
    room = data.get('room')
    if room:
        join_room(room)
        emit('status', {'message': f'انضممت للغرفة {room}'})

def send_notification_update(notification):
    """إرسال تحديث التنبيهات"""
    socketio.emit('notification', {
        'type': notification['type'],
        'title': notification['title'],
        'message': notification['message'],
        'severity': notification['severity'],
        'timestamp': notification['created_at'].isoformat()
    })

# إضافة callback للتنبيهات
notification_manager.add_callback(send_notification_update)

@app.errorhandler(404)
def not_found(error):
    """صفحة 404"""
    return render_template('404.html'), 404

@app.errorhandler(500)
def internal_error(error):
    """صفحة 500"""
    logger.error(f"خطأ داخلي: {error}")
    return render_template('500.html'), 500

if __name__ == '__main__':
    if init_app():
        logger.info("🚀 بدء تشغيل Cyber Shield Web App")
        logger.info(f"🌐 الرابط: http://localhost:5000")
        logger.info(f"🇵🇸 Cyper Member | State of Palestine")
        
        # تشغيل التطبيق
        socketio.run(app, 
                    host='0.0.0.0', 
                    port=5000, 
                    debug=True,
                    allow_unsafe_werkzeug=True)
    else:
        logger.error("❌ فشل في تهيئة التطبيق")
        sys.exit(1)
