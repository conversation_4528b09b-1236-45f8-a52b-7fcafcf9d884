# -*- coding: utf-8 -*-
"""
Cyber Shield - Authentication Manager
مدير المصادقة والأمان
"""

import logging
import secrets
import smtplib
import socket
import platform
import psutil
import hashlib
from datetime import datetime, timedelta
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from typing import Optional, Dict, Tuple, List
import json
import requests

from src.database.db_manager import DatabaseManager
from src.auth.password_manager import PasswordManager
from src.auth.session_manager import SessionManager
from config.settings import *

logger = logging.getLogger(__name__)

class AuthenticationManager:
    """
    مدير المصادقة والأمان
    """
    
    def __init__(self):
        self.db_manager = DatabaseManager()
        self.password_manager = PasswordManager()
        self.session_manager = SessionManager()
        self.max_login_attempts = 5
        self.lockout_duration = timedelta(minutes=30)

        # معلومات النظام
        self.system_info = self._get_system_info()

        # فحص الاتصال بالإنترنت
        self.internet_connected = self._check_internet_connection()
    
    def register_user(self, username: str, email: str, password: str, 
                     full_name: str, phone: str = None) -> Tuple[bool, str]:
        """
        تسجيل مستخدم جديد
        
        Args:
            username: اسم المستخدم
            email: البريد الإلكتروني
            password: كلمة المرور
            full_name: الاسم الكامل
            phone: رقم الهاتف (اختياري)
            
        Returns:
            tuple: (success, message)
        """
        try:
            # التحقق من صحة البيانات
            if not self._validate_registration_data(username, email, password, full_name):
                return False, "بيانات غير صحيحة"
            
            # التحقق من عدم وجود المستخدم مسبقاً
            if self.db_manager.get_user_by_username(username):
                return False, "اسم المستخدم موجود مسبقاً"
            
            if self.db_manager.get_user_by_email(email):
                return False, "البريد الإلكتروني مسجل مسبقاً"
            
            # تشفير كلمة المرور
            password_hash, salt = self.password_manager.hash_password(password)
            
            # إنشاء بيانات المستخدم
            user_data = {
                'username': username,
                'email': email,
                'password_hash': password_hash,
                'salt': salt,
                'full_name': full_name,
                'phone': phone,
                'is_admin': False,
                'email_verified': False,
                'two_factor_enabled': False
            }
            
            # إنشاء المستخدم في قاعدة البيانات
            user_id = self.db_manager.create_user(user_data)
            
            # إرسال رمز تحقق البريد الإلكتروني
            verification_sent = self._send_email_verification(user_id, email)
            
            # تسجيل النشاط
            self.db_manager.log_activity(
                user_id, 
                'USER_REGISTERED', 
                f'تم تسجيل مستخدم جديد: {username}',
                severity='INFO'
            )
            
            message = "تم إنشاء الحساب بنجاح"
            if verification_sent:
                message += ". تم إرسال رمز التحقق إلى بريدك الإلكتروني"
            
            return True, message
            
        except Exception as e:
            logger.error(f"❌ خطأ في تسجيل المستخدم: {e}")
            return False, "حدث خطأ في إنشاء الحساب"
    
    def login_user(self, username_or_email: str, password: str, 
                  ip_address: str = None, user_agent: str = None) -> Tuple[bool, str, Dict]:
        """
        تسجيل دخول المستخدم
        
        Args:
            username_or_email: اسم المستخدم أو البريد الإلكتروني
            password: كلمة المرور
            ip_address: عنوان IP
            user_agent: معلومات المتصفح
            
        Returns:
            tuple: (success, message, user_data)
        """
        try:
            # البحث عن المستخدم
            user = self._get_user_by_login(username_or_email)
            
            if not user:
                self._log_failed_login(None, username_or_email, ip_address, "مستخدم غير موجود")
                return False, "اسم المستخدم أو كلمة المرور غير صحيحة", {}
            
            # التحقق من حالة الحساب
            if not user['is_active']:
                self._log_failed_login(user['id'], username_or_email, ip_address, "حساب معطل")
                return False, "الحساب معطل", {}
            
            # التحقق من قفل الحساب
            if self._is_account_locked(user):
                return False, "الحساب مقفل مؤقتاً بسبب محاولات دخول فاشلة", {}
            
            # التحقق من كلمة المرور
            if not self.password_manager.verify_password(password, user['password_hash'], user['salt']):
                self._handle_failed_login(user, ip_address)
                return False, "اسم المستخدم أو كلمة المرور غير صحيحة", {}
            
            # إعادة تعيين محاولات الدخول الفاشلة
            self._reset_login_attempts(user['id'])
            
            # إنشاء جلسة جديدة
            session_token = self._create_session(user, ip_address, user_agent)
            
            # تحديث آخر دخول
            self._update_last_login(user['id'])
            
            # تسجيل النشاط
            self.db_manager.log_activity(
                user['id'],
                'USER_LOGIN',
                f'تسجيل دخول ناجح للمستخدم: {user["username"]}',
                ip_address,
                'INFO'
            )
            
            # إعداد بيانات المستخدم للإرجاع
            user_data = {
                'id': user['id'],
                'username': user['username'],
                'email': user['email'],
                'full_name': user['full_name'],
                'is_admin': user['is_admin'],
                'session_token': session_token,
                'two_factor_enabled': user['two_factor_enabled']
            }
            
            # التحقق من التحقق بخطوتين
            if user['two_factor_enabled']:
                # إرسال رمز التحقق
                verification_sent = self._send_2fa_code(user['id'], user['email'])
                if verification_sent:
                    return True, "تم إرسال رمز التحقق إلى بريدك الإلكتروني", user_data
                else:
                    return False, "خطأ في إرسال رمز التحقق", {}
            
            return True, "تم تسجيل الدخول بنجاح", user_data
            
        except Exception as e:
            logger.error(f"❌ خطأ في تسجيل الدخول: {e}")
            return False, "حدث خطأ في تسجيل الدخول", {}
    
    def verify_2fa_code(self, user_id: int, code: str) -> Tuple[bool, str]:
        """
        التحقق من رمز التحقق بخطوتين
        """
        try:
            query = """
                SELECT * FROM verification_codes 
                WHERE user_id = ? AND code = ? AND code_type = '2fa' 
                AND used = FALSE AND expires_at > CURRENT_TIMESTAMP
                ORDER BY created_at DESC LIMIT 1
            """
            
            results = self.db_manager.execute_query(query, (user_id, code))
            
            if not results:
                return False, "رمز التحقق غير صحيح أو منتهي الصلاحية"
            
            # تحديد الرمز كمستخدم
            update_query = "UPDATE verification_codes SET used = TRUE WHERE id = ?"
            self.db_manager.execute_query(update_query, (results[0]['id'],))
            
            # تسجيل النشاط
            self.db_manager.log_activity(
                user_id,
                '2FA_VERIFIED',
                'تم التحقق بنجاح من رمز التحقق بخطوتين',
                severity='INFO'
            )
            
            return True, "تم التحقق بنجاح"
            
        except Exception as e:
            logger.error(f"❌ خطأ في التحقق من رمز 2FA: {e}")
            return False, "حدث خطأ في التحقق"
    
    def logout_user(self, session_token: str) -> bool:
        """
        تسجيل خروج المستخدم
        """
        try:
            # إلغاء تفعيل الجلسة
            query = "UPDATE sessions SET is_active = FALSE WHERE session_token = ?"
            self.db_manager.execute_query(query, (session_token,))
            
            return True
            
        except Exception as e:
            logger.error(f"❌ خطأ في تسجيل الخروج: {e}")
            return False
    
    def _validate_registration_data(self, username: str, email: str, password: str, full_name: str) -> bool:
        """
        التحقق من صحة بيانات التسجيل
        """
        # التحقق من اسم المستخدم
        if not username or len(username) < 3:
            return False
        
        # التحقق من البريد الإلكتروني
        import re
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(email_pattern, email):
            return False
        
        # التحقق من قوة كلمة المرور
        is_valid, _ = self.password_manager.validate_password_strength(password)
        if not is_valid:
            return False
        
        # التحقق من الاسم الكامل
        if not full_name or len(full_name) < 2:
            return False
        
        return True
    
    def _get_user_by_login(self, username_or_email: str) -> Optional[Dict]:
        """
        البحث عن المستخدم بواسطة اسم المستخدم أو البريد الإلكتروني
        """
        # محاولة البحث بواسطة اسم المستخدم أولاً
        user = self.db_manager.get_user_by_username(username_or_email)
        
        # إذا لم يوجد، البحث بواسطة البريد الإلكتروني
        if not user:
            user = self.db_manager.get_user_by_email(username_or_email)
        
        return user
    
    def _is_account_locked(self, user: Dict) -> bool:
        """
        التحقق من قفل الحساب
        """
        if user['locked_until']:
            locked_until = datetime.fromisoformat(user['locked_until'])
            return datetime.now() < locked_until
        return False
    
    def _handle_failed_login(self, user: Dict, ip_address: str):
        """
        التعامل مع محاولة دخول فاشلة
        """
        # زيادة عدد المحاولات الفاشلة
        new_attempts = user['login_attempts'] + 1
        
        # قفل الحساب إذا تجاوز الحد الأقصى
        locked_until = None
        if new_attempts >= self.max_login_attempts:
            locked_until = datetime.now() + self.lockout_duration
        
        # تحديث قاعدة البيانات
        query = """
            UPDATE users 
            SET login_attempts = ?, locked_until = ?
            WHERE id = ?
        """
        self.db_manager.execute_query(query, (new_attempts, locked_until, user['id']))
        
        # تسجيل النشاط
        self._log_failed_login(user['id'], user['username'], ip_address, "كلمة مرور خاطئة")
    
    def _log_failed_login(self, user_id: Optional[int], username: str, ip_address: str, reason: str):
        """
        تسجيل محاولة دخول فاشلة
        """
        self.db_manager.log_activity(
            user_id,
            'LOGIN_FAILED',
            f'محاولة دخول فاشلة للمستخدم: {username} - السبب: {reason}',
            ip_address,
            'WARNING'
        )

    def _reset_login_attempts(self, user_id: int):
        """
        إعادة تعيين محاولات الدخول الفاشلة
        """
        query = """
            UPDATE users
            SET login_attempts = 0, locked_until = NULL
            WHERE id = ?
        """
        self.db_manager.execute_query(query, (user_id,))

    def _create_session(self, user: Dict, ip_address: str, user_agent: str) -> str:
        """
        إنشاء جلسة جديدة للمستخدم
        """
        session_token = self.password_manager.generate_session_token()
        expires_at = datetime.now() + timedelta(seconds=SESSION_TIMEOUT)

        # الحصول على معلومات الموقع
        location_data = self._get_location_data(ip_address)

        query = """
            INSERT INTO sessions (
                user_id, session_token, ip_address, user_agent,
                location_data, expires_at
            ) VALUES (?, ?, ?, ?, ?, ?)
        """

        self.db_manager.execute_query(query, (
            user['id'],
            session_token,
            ip_address,
            user_agent,
            json.dumps(location_data) if location_data else None,
            expires_at
        ))

        return session_token

    def _update_last_login(self, user_id: int):
        """
        تحديث آخر دخول للمستخدم
        """
        query = "UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = ?"
        self.db_manager.execute_query(query, (user_id,))

    def _send_email_verification(self, user_id: int, email: str) -> bool:
        """
        إرسال رمز تحقق البريد الإلكتروني
        """
        try:
            # إنشاء رمز التحقق
            verification_code = self.password_manager.generate_verification_code()
            expires_at = datetime.now() + timedelta(minutes=30)

            # حفظ الرمز في قاعدة البيانات
            query = """
                INSERT INTO verification_codes (
                    user_id, code, code_type, expires_at
                ) VALUES (?, ?, ?, ?)
            """
            self.db_manager.execute_query(query, (
                user_id, verification_code, 'email_verification', expires_at
            ))

            # إرسال البريد الإلكتروني
            return self._send_email(
                email,
                "تحقق من بريدك الإلكتروني - Cyber Shield",
                f"""
                مرحباً،

                رمز التحقق الخاص بك هو: {verification_code}

                هذا الرمز صالح لمدة 30 دقيقة.

                مع تحيات فريق Cyber Shield - Cyper Member | State of Palestine
                """
            )

        except Exception as e:
            logger.error(f"❌ خطأ في إرسال رمز التحقق: {e}")
            return False

    def _send_2fa_code(self, user_id: int, email: str) -> bool:
        """
        إرسال رمز التحقق بخطوتين
        """
        try:
            # إنشاء رمز التحقق
            verification_code = self.password_manager.generate_verification_code()
            expires_at = datetime.now() + timedelta(minutes=5)

            # حفظ الرمز في قاعدة البيانات
            query = """
                INSERT INTO verification_codes (
                    user_id, code, code_type, expires_at
                ) VALUES (?, ?, ?, ?)
            """
            self.db_manager.execute_query(query, (
                user_id, verification_code, '2fa', expires_at
            ))

            # إرسال البريد الإلكتروني
            return self._send_email(
                email,
                "رمز التحقق بخطوتين - Cyber Shield",
                f"""
                رمز التحقق بخطوتين الخاص بك هو: {verification_code}

                هذا الرمز صالح لمدة 5 دقائق فقط.

                إذا لم تطلب هذا الرمز، يرجى تجاهل هذه الرسالة.

                مع تحيات فريق Cyber Shield - Cyper Member | State of Palestine
                """
            )

        except Exception as e:
            logger.error(f"❌ خطأ في إرسال رمز 2FA: {e}")
            return False

    def _send_email(self, to_email: str, subject: str, body: str) -> bool:
        """
        إرسال بريد إلكتروني
        """
        try:
            # التحقق من إعدادات البريد الإلكتروني
            if not EMAIL_SENDER or not EMAIL_PASSWORD:
                logger.warning("⚠️ إعدادات البريد الإلكتروني غير مكتملة")
                return False

            # إنشاء الرسالة
            msg = MIMEMultipart()
            msg['From'] = EMAIL_SENDER
            msg['To'] = to_email
            msg['Subject'] = subject

            msg.attach(MIMEText(body, 'plain', 'utf-8'))

            # إرسال البريد
            server = smtplib.SMTP(SMTP_SERVER, SMTP_PORT)
            server.starttls()
            server.login(EMAIL_SENDER, EMAIL_PASSWORD)
            text = msg.as_string()
            server.sendmail(EMAIL_SENDER, to_email, text)
            server.quit()

            return True

        except Exception as e:
            logger.error(f"❌ خطأ في إرسال البريد الإلكتروني: {e}")
            return False

    def _get_location_data(self, ip_address: str) -> Optional[Dict]:
        """
        الحصول على معلومات الموقع من عنوان IP
        """
        try:
            if not ip_address or ip_address in ['127.0.0.1', 'localhost']:
                return None

            response = requests.get(f"{IP_GEOLOCATION_API}{ip_address}", timeout=5)
            if response.status_code == 200:
                return response.json()

        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على معلومات الموقع: {e}")

        return None

    def validate_session(self, session_token: str) -> Optional[Dict]:
        """
        التحقق من صحة الجلسة
        """
        try:
            query = """
                SELECT s.*, u.username, u.full_name, u.is_admin
                FROM sessions s
                JOIN users u ON s.user_id = u.id
                WHERE s.session_token = ? AND s.is_active = TRUE
                AND s.expires_at > CURRENT_TIMESTAMP
            """

            results = self.db_manager.execute_query(query, (session_token,))
            return results[0] if results else None

        except Exception as e:
            logger.error(f"❌ خطأ في التحقق من الجلسة: {e}")
            return None

    def _get_system_info(self) -> Dict:
        """
        الحصول على معلومات النظام
        """
        try:
            return {
                'platform': platform.system(),
                'platform_release': platform.release(),
                'platform_version': platform.version(),
                'architecture': platform.machine(),
                'hostname': socket.gethostname(),
                'ip_address': socket.gethostbyname(socket.gethostname()),
                'processor': platform.processor(),
                'ram': str(round(psutil.virtual_memory().total / (1024.0 **3)))+" GB",
                'cpu_count': psutil.cpu_count(),
                'boot_time': datetime.fromtimestamp(psutil.boot_time()).strftime("%Y-%m-%d %H:%M:%S")
            }
        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على معلومات النظام: {e}")
            return {}

    def _check_internet_connection(self) -> bool:
        """
        فحص الاتصال بالإنترنت
        """
        try:
            # محاولة الاتصال بـ Google DNS
            socket.create_connection(("*******", 53), timeout=3)
            return True
        except OSError:
            try:
                # محاولة الاتصال بـ Cloudflare DNS
                socket.create_connection(("*******", 53), timeout=3)
                return True
            except OSError:
                return False

    def get_public_ip(self) -> str:
        """
        الحصول على عنوان IP العام
        """
        try:
            if not self.internet_connected:
                return "غير متصل بالإنترنت"

            # محاولة عدة خدمات للحصول على IP العام
            services = [
                "https://api.ipify.org",
                "https://ipinfo.io/ip",
                "https://icanhazip.com"
            ]

            for service in services:
                try:
                    response = requests.get(service, timeout=5)
                    if response.status_code == 200:
                        return response.text.strip()
                except:
                    continue

            return "غير متاح"

        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على IP العام: {e}")
            return "خطأ"

    def get_network_interfaces(self) -> Dict:
        """
        الحصول على واجهات الشبكة
        """
        try:
            interfaces = {}
            for interface, addrs in psutil.net_if_addrs().items():
                interface_info = []
                for addr in addrs:
                    if addr.family == socket.AF_INET:  # IPv4
                        interface_info.append({
                            'ip': addr.address,
                            'netmask': addr.netmask,
                            'broadcast': addr.broadcast
                        })
                if interface_info:
                    interfaces[interface] = interface_info
            return interfaces
        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على واجهات الشبكة: {e}")
            return {}

    def get_system_security_status(self) -> Dict:
        """
        الحصول على حالة أمان النظام
        """
        try:
            status = {
                'firewall_enabled': self._check_firewall_status(),
                'antivirus_running': self._check_antivirus_status(),
                'windows_defender': self._check_windows_defender(),
                'system_updates': self._check_system_updates(),
                'disk_encryption': self._check_disk_encryption(),
                'network_security': self._check_network_security()
            }
            return status
        except Exception as e:
            logger.error(f"❌ خطأ في فحص حالة الأمان: {e}")
            return {}

    def _check_firewall_status(self) -> bool:
        """
        فحص حالة جدار الحماية
        """
        try:
            if platform.system() == "Windows":
                import subprocess
                result = subprocess.run(
                    ["netsh", "advfirewall", "show", "allprofiles", "state"],
                    capture_output=True, text=True, timeout=10
                )
                return "ON" in result.stdout
            return False
        except:
            return False

    def _check_antivirus_status(self) -> bool:
        """
        فحص حالة مكافح الفيروسات
        """
        try:
            # فحص العمليات الجارية للبحث عن برامج مكافحة الفيروسات
            antivirus_processes = [
                'avp.exe', 'avgnt.exe', 'avguard.exe', 'bdagent.exe',
                'egui.exe', 'ekrn.exe', 'msmpeng.exe', 'windefend'
            ]

            for proc in psutil.process_iter(['name']):
                if proc.info['name'] and proc.info['name'].lower() in antivirus_processes:
                    return True
            return False
        except:
            return False

    def _check_windows_defender(self) -> bool:
        """
        فحص Windows Defender
        """
        try:
            if platform.system() == "Windows":
                import subprocess
                result = subprocess.run(
                    ["powershell", "Get-MpComputerStatus | Select-Object -ExpandProperty RealTimeProtectionEnabled"],
                    capture_output=True, text=True, timeout=10
                )
                return "True" in result.stdout
            return False
        except:
            return False

    def _check_system_updates(self) -> Dict:
        """
        فحص تحديثات النظام
        """
        try:
            if platform.system() == "Windows":
                import subprocess
                result = subprocess.run(
                    ["powershell", "Get-WUList | Measure-Object | Select-Object -ExpandProperty Count"],
                    capture_output=True, text=True, timeout=30
                )
                pending_updates = result.stdout.strip()
                return {
                    'pending_updates': int(pending_updates) if pending_updates.isdigit() else 0,
                    'last_check': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                }
            return {'pending_updates': 0, 'last_check': 'غير متاح'}
        except:
            return {'pending_updates': 0, 'last_check': 'خطأ'}

    def _check_disk_encryption(self) -> bool:
        """
        فحص تشفير القرص
        """
        try:
            if platform.system() == "Windows":
                import subprocess
                result = subprocess.run(
                    ["manage-bde", "-status"],
                    capture_output=True, text=True, timeout=10
                )
                return "Protection On" in result.stdout
            return False
        except:
            return False

    def _check_network_security(self) -> Dict:
        """
        فحص أمان الشبكة
        """
        try:
            return {
                'open_ports': self._scan_open_ports(),
                'suspicious_connections': self._check_suspicious_connections(),
                'dns_servers': self._get_dns_servers()
            }
        except Exception as e:
            logger.error(f"❌ خطأ في فحص أمان الشبكة: {e}")
            return {}

    def _scan_open_ports(self) -> List:
        """
        فحص المنافذ المفتوحة
        """
        try:
            connections = psutil.net_connections(kind='inet')
            open_ports = []
            for conn in connections:
                if conn.status == 'LISTEN':
                    open_ports.append({
                        'port': conn.laddr.port,
                        'address': conn.laddr.ip,
                        'pid': conn.pid
                    })
            return open_ports[:20]  # أول 20 منفذ
        except:
            return []

    def _check_suspicious_connections(self) -> List:
        """
        فحص الاتصالات المشبوهة
        """
        try:
            connections = psutil.net_connections(kind='inet')
            suspicious = []

            for conn in connections:
                if conn.raddr and conn.status == 'ESTABLISHED':
                    # فحص الاتصالات الخارجية
                    if not conn.raddr.ip.startswith(('192.168.', '10.', '172.')):
                        suspicious.append({
                            'remote_ip': conn.raddr.ip,
                            'remote_port': conn.raddr.port,
                            'local_port': conn.laddr.port,
                            'pid': conn.pid
                        })

            return suspicious[:10]  # أول 10 اتصالات مشبوهة
        except:
            return []

    def _get_dns_servers(self) -> List:
        """
        الحصول على خوادم DNS
        """
        try:
            if platform.system() == "Windows":
                import subprocess
                result = subprocess.run(
                    ["nslookup", "google.com"],
                    capture_output=True, text=True, timeout=10
                )
                # استخراج خوادم DNS من النتيجة
                lines = result.stdout.split('\n')
                dns_servers = []
                for line in lines:
                    if 'Server:' in line:
                        dns_servers.append(line.split(':')[1].strip())
                return dns_servers
            return []
        except:
            return []
