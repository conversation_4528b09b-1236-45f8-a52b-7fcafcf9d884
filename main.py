#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cyber Shield - Cyper Member | State of Palestine
الملف الرئيسي للتطبيق

المطور: Cyper Member | State of Palestine
الإصدار: 1.0.0
التاريخ: 2025
"""

import sys
import os
import logging
from pathlib import Path

# إضافة مسار المشروع إلى sys.path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# استيراد الإعدادات
from config.settings import *

# إعداد نظام السجلات
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(LOGS_DIR / 'app.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

def initialize_services():
    """
    تهيئة الخدمات الأساسية
    """
    try:
        # تهيئة قاعدة البيانات
        from src.database.db_manager import DatabaseManager
        db_manager = DatabaseManager()
        db_manager.initialize_database()
        logger.info("✅ تم تهيئة قاعدة البيانات")

        # تهيئة مدير التنبيهات
        from src.utils.notification_manager import NotificationManager
        notification_manager = NotificationManager()
        logger.info("✅ تم تهيئة مدير التنبيهات")

        # تهيئة مدير التحديثات
        from src.utils.update_manager import UpdateManager
        update_manager = UpdateManager()
        logger.info("✅ تم تهيئة مدير التحديثات")

        # إرسال تنبيه بدء التشغيل
        notification_manager.send_system_notification(
            'SYSTEM_START',
            f'تم بدء تشغيل Cyber Shield بنجاح - الإصدار {APP_VERSION}',
            'INFO'
        )

        return {
            'db_manager': db_manager,
            'notification_manager': notification_manager,
            'update_manager': update_manager
        }

    except Exception as e:
        logger.error(f"❌ خطأ في تهيئة الخدمات: {e}")
        raise

def main():
    """
    الدالة الرئيسية لتشغيل التطبيق
    """
    try:
        logger.info("🚀 بدء تشغيل Cyber Shield - Cyper Member")
        logger.info(f"🇵🇸 Cyper Member | State of Palestine")
        logger.info(f"📍 الإصدار: {APP_VERSION}")
        logger.info(f"👨‍💻 المطور: {APP_AUTHOR}")

        # التحقق من متطلبات النظام
        check_system_requirements()

        # إنشاء المجلدات المطلوبة
        for directory in [DATA_DIR, TEMP_DIR, LOGS_DIR, ASSETS_DIR]:
            directory.mkdir(exist_ok=True)

        # تهيئة الخدمات
        services = initialize_services()

        # تشغيل الواجهة الرسومية
        from src.gui.main_window import CyberShieldApp
        app = CyberShieldApp()

        # ربط الخدمات بالتطبيق
        app.notification_manager = services['notification_manager']
        app.update_manager = services['update_manager']

        app.run()

    except KeyboardInterrupt:
        logger.info("🛑 تم إيقاف التطبيق بواسطة المستخدم")
        sys.exit(0)
    except Exception as e:
        logger.error(f"❌ خطأ في تشغيل التطبيق: {e}")
        sys.exit(1)
    finally:
        # تنظيف الموارد
        try:
            if 'services' in locals():
                if 'notification_manager' in services:
                    services['notification_manager'].stop_notification_processor()
                if 'update_manager' in services:
                    services['update_manager'].stop_auto_check()
            logger.info("🧹 تم تنظيف الموارد")
        except:
            pass

def check_system_requirements():
    """
    التحقق من متطلبات النظام
    """
    logger.info("🔍 التحقق من متطلبات النظام...")
    
    # التحقق من نظام التشغيل
    if sys.platform != "win32":
        logger.warning("⚠️ هذا التطبيق مصمم لنظام Windows")
    
    # التحقق من إصدار Python
    if sys.version_info < (3, 8):
        logger.error("❌ يتطلب Python 3.8 أو أحدث")
        sys.exit(1)
    
    # إنشاء المجلدات المطلوبة
    for directory in [ASSETS_DIR, DATABASE_DIR, LOGS_DIR, TEMP_DIR]:
        directory.mkdir(exist_ok=True)
        logger.info(f"📁 تم إنشاء/التحقق من المجلد: {directory}")
    
    logger.info("✅ تم التحقق من متطلبات النظام بنجاح")

if __name__ == "__main__":
    # عرض معلومات التطبيق
    print("=" * 60)
    print(f"🛡️  {APP_NAME}")
    print(f"📍 الإصدار: {APP_VERSION}")
    print(f"👨‍💻 {APP_AUTHOR}")
    print(f"📝 {APP_DESCRIPTION}")
    print("=" * 60)
    print()
    
    # تشغيل التطبيق
    main()
