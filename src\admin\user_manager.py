# -*- coding: utf-8 -*-
"""
Cyber Shield - User Manager
مدير المستخدمين للوحة الإدارة
"""

import logging
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta

from src.database.db_manager import DatabaseManager
from src.auth.password_manager import PasswordManager
from src.auth.permissions import Permission, require_permission
from config.settings import *

logger = logging.getLogger(__name__)

class UserManager:
    """
    مدير المستخدمين للوحة الإدارة
    """
    
    def __init__(self):
        self.db_manager = DatabaseManager()
        self.password_manager = PasswordManager()
    
    @require_permission(Permission.MANAGE_USERS)
    def get_all_users(self, user_data: Dict) -> List[Dict]:
        """
        الحصول على جميع المستخدمين
        """
        try:
            query = """
                SELECT id, username, email, full_name, phone, is_admin, 
                       is_active, email_verified, two_factor_enabled,
                       created_at, updated_at, last_login, login_attempts,
                       locked_until, profile_image
                FROM users 
                ORDER BY created_at DESC
            """
            
            users = self.db_manager.execute_query(query)
            
            # إضافة إحصائيات لكل مستخدم
            for user in users:
                user['statistics'] = self._get_user_statistics(user['id'])
            
            return users
            
        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على المستخدمين: {e}")
            return []
    
    @require_permission(Permission.MANAGE_USERS)
    def get_user_by_id(self, user_data: Dict, user_id: int) -> Optional[Dict]:
        """
        الحصول على مستخدم بواسطة المعرف
        """
        try:
            query = """
                SELECT * FROM users WHERE id = ?
            """
            
            results = self.db_manager.execute_query(query, (user_id,))
            
            if results:
                user = results[0]
                user['statistics'] = self._get_user_statistics(user_id)
                user['recent_activities'] = self._get_user_recent_activities(user_id)
                user['active_sessions'] = self._get_user_active_sessions(user_id)
                return user
            
            return None
            
        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على المستخدم {user_id}: {e}")
            return None
    
    @require_permission(Permission.MANAGE_USERS)
    def create_user(self, user_data: Dict, new_user_data: Dict) -> Tuple[bool, str]:
        """
        إنشاء مستخدم جديد
        """
        try:
            # التحقق من البيانات المطلوبة
            required_fields = ['username', 'email', 'password', 'full_name']
            for field in required_fields:
                if not new_user_data.get(field):
                    return False, f"الحقل {field} مطلوب"
            
            # التحقق من عدم وجود المستخدم مسبقاً
            if self.db_manager.get_user_by_username(new_user_data['username']):
                return False, "اسم المستخدم موجود مسبقاً"
            
            if self.db_manager.get_user_by_email(new_user_data['email']):
                return False, "البريد الإلكتروني مسجل مسبقاً"
            
            # التحقق من قوة كلمة المرور
            is_valid, errors = self.password_manager.validate_password_strength(new_user_data['password'])
            if not is_valid:
                return False, f"كلمة المرور ضعيفة: {', '.join(errors)}"
            
            # تشفير كلمة المرور
            password_hash, salt = self.password_manager.hash_password(new_user_data['password'])
            
            # إنشاء المستخدم
            user_creation_data = {
                'username': new_user_data['username'],
                'email': new_user_data['email'],
                'password_hash': password_hash,
                'salt': salt,
                'full_name': new_user_data['full_name'],
                'phone': new_user_data.get('phone'),
                'is_admin': new_user_data.get('is_admin', False),
                'email_verified': new_user_data.get('email_verified', False),
                'two_factor_enabled': new_user_data.get('two_factor_enabled', False)
            }
            
            new_user_id = self.db_manager.create_user(user_creation_data)
            
            # تسجيل النشاط
            self.db_manager.log_activity(
                user_data['id'],
                'USER_CREATED',
                f'تم إنشاء مستخدم جديد: {new_user_data["username"]}',
                severity='INFO'
            )
            
            logger.info(f"✅ تم إنشاء مستخدم جديد: {new_user_data['username']}")
            return True, f"تم إنشاء المستخدم بنجاح (ID: {new_user_id})"
            
        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء المستخدم: {e}")
            return False, "حدث خطأ في إنشاء المستخدم"
    
    @require_permission(Permission.MANAGE_USERS)
    def update_user(self, user_data: Dict, user_id: int, update_data: Dict) -> Tuple[bool, str]:
        """
        تحديث بيانات المستخدم
        """
        try:
            # التحقق من وجود المستخدم
            existing_user = self.get_user_by_id(user_data, user_id)
            if not existing_user:
                return False, "المستخدم غير موجود"
            
            # بناء استعلام التحديث
            update_fields = []
            update_values = []
            
            allowed_fields = [
                'username', 'email', 'full_name', 'phone', 
                'is_admin', 'is_active', 'email_verified', 'two_factor_enabled'
            ]
            
            for field in allowed_fields:
                if field in update_data:
                    update_fields.append(f"{field} = ?")
                    update_values.append(update_data[field])
            
            # تحديث كلمة المرور إذا تم توفيرها
            if 'password' in update_data and update_data['password']:
                is_valid, errors = self.password_manager.validate_password_strength(update_data['password'])
                if not is_valid:
                    return False, f"كلمة المرور ضعيفة: {', '.join(errors)}"
                
                password_hash, salt = self.password_manager.hash_password(update_data['password'])
                update_fields.extend(['password_hash = ?', 'salt = ?'])
                update_values.extend([password_hash, salt])
            
            if not update_fields:
                return False, "لا توجد بيانات للتحديث"
            
            # إضافة تاريخ التحديث
            update_fields.append('updated_at = CURRENT_TIMESTAMP')
            update_values.append(user_id)
            
            # تنفيذ التحديث
            query = f"UPDATE users SET {', '.join(update_fields)} WHERE id = ?"
            self.db_manager.execute_query(query, tuple(update_values))
            
            # تسجيل النشاط
            self.db_manager.log_activity(
                user_data['id'],
                'USER_UPDATED',
                f'تم تحديث بيانات المستخدم: {existing_user["username"]}',
                severity='INFO'
            )
            
            logger.info(f"✅ تم تحديث المستخدم: {existing_user['username']}")
            return True, "تم تحديث بيانات المستخدم بنجاح"
            
        except Exception as e:
            logger.error(f"❌ خطأ في تحديث المستخدم {user_id}: {e}")
            return False, "حدث خطأ في تحديث المستخدم"
    
    @require_permission(Permission.MANAGE_USERS)
    def delete_user(self, user_data: Dict, user_id: int) -> Tuple[bool, str]:
        """
        حذف مستخدم
        """
        try:
            # التحقق من وجود المستخدم
            existing_user = self.get_user_by_id(user_data, user_id)
            if not existing_user:
                return False, "المستخدم غير موجود"
            
            # منع حذف المدير الرئيسي
            if existing_user['username'] == 'admin':
                return False, "لا يمكن حذف المدير الرئيسي"
            
            # منع المستخدم من حذف نفسه
            if user_id == user_data['id']:
                return False, "لا يمكنك حذف حسابك الخاص"
            
            # حذف المستخدم (سيتم حذف البيانات المرتبطة تلقائياً بسبب CASCADE)
            query = "DELETE FROM users WHERE id = ?"
            self.db_manager.execute_query(query, (user_id,))
            
            # تسجيل النشاط
            self.db_manager.log_activity(
                user_data['id'],
                'USER_DELETED',
                f'تم حذف المستخدم: {existing_user["username"]}',
                severity='WARNING'
            )
            
            logger.warning(f"⚠️ تم حذف المستخدم: {existing_user['username']}")
            return True, "تم حذف المستخدم بنجاح"
            
        except Exception as e:
            logger.error(f"❌ خطأ في حذف المستخدم {user_id}: {e}")
            return False, "حدث خطأ في حذف المستخدم"
    
    @require_permission(Permission.MANAGE_USERS)
    def lock_user(self, user_data: Dict, user_id: int, duration_hours: int = 24) -> Tuple[bool, str]:
        """
        قفل حساب مستخدم
        """
        try:
            # التحقق من وجود المستخدم
            existing_user = self.get_user_by_id(user_data, user_id)
            if not existing_user:
                return False, "المستخدم غير موجود"
            
            # منع قفل المدير الرئيسي
            if existing_user['username'] == 'admin':
                return False, "لا يمكن قفل المدير الرئيسي"
            
            # حساب وقت انتهاء القفل
            locked_until = datetime.now() + timedelta(hours=duration_hours)
            
            # تحديث حالة القفل
            query = "UPDATE users SET locked_until = ? WHERE id = ?"
            self.db_manager.execute_query(query, (locked_until, user_id))
            
            # إلغاء تفعيل جميع جلسات المستخدم
            from src.auth.session_manager import SessionManager
            session_manager = SessionManager()
            session_manager.invalidate_user_sessions(user_id)
            
            # تسجيل النشاط
            self.db_manager.log_activity(
                user_data['id'],
                'USER_LOCKED',
                f'تم قفل المستخدم: {existing_user["username"]} لمدة {duration_hours} ساعة',
                severity='WARNING'
            )
            
            logger.warning(f"🔒 تم قفل المستخدم: {existing_user['username']}")
            return True, f"تم قفل المستخدم لمدة {duration_hours} ساعة"
            
        except Exception as e:
            logger.error(f"❌ خطأ في قفل المستخدم {user_id}: {e}")
            return False, "حدث خطأ في قفل المستخدم"
    
    @require_permission(Permission.MANAGE_USERS)
    def unlock_user(self, user_data: Dict, user_id: int) -> Tuple[bool, str]:
        """
        إلغاء قفل حساب مستخدم
        """
        try:
            # التحقق من وجود المستخدم
            existing_user = self.get_user_by_id(user_data, user_id)
            if not existing_user:
                return False, "المستخدم غير موجود"
            
            # إلغاء القفل وإعادة تعيين محاولات الدخول
            query = """
                UPDATE users 
                SET locked_until = NULL, login_attempts = 0 
                WHERE id = ?
            """
            self.db_manager.execute_query(query, (user_id,))
            
            # تسجيل النشاط
            self.db_manager.log_activity(
                user_data['id'],
                'USER_UNLOCKED',
                f'تم إلغاء قفل المستخدم: {existing_user["username"]}',
                severity='INFO'
            )
            
            logger.info(f"🔓 تم إلغاء قفل المستخدم: {existing_user['username']}")
            return True, "تم إلغاء قفل المستخدم بنجاح"
            
        except Exception as e:
            logger.error(f"❌ خطأ في إلغاء قفل المستخدم {user_id}: {e}")
            return False, "حدث خطأ في إلغاء قفل المستخدم"
    
    def _get_user_statistics(self, user_id: int) -> Dict:
        """
        الحصول على إحصائيات المستخدم
        """
        try:
            stats = {
                'total_logins': 0,
                'last_login': None,
                'active_sessions': 0,
                'total_activities': 0,
                'security_events': 0
            }
            
            # إجمالي تسجيلات الدخول
            query = """
                SELECT COUNT(*) as count FROM activity_logs 
                WHERE user_id = ? AND action = 'USER_LOGIN'
            """
            result = self.db_manager.execute_query(query, (user_id,))
            stats['total_logins'] = result[0]['count'] if result else 0
            
            # آخر تسجيل دخول
            query = """
                SELECT created_at FROM activity_logs 
                WHERE user_id = ? AND action = 'USER_LOGIN'
                ORDER BY created_at DESC LIMIT 1
            """
            result = self.db_manager.execute_query(query, (user_id,))
            stats['last_login'] = result[0]['created_at'] if result else None
            
            # الجلسات النشطة
            query = """
                SELECT COUNT(*) as count FROM sessions 
                WHERE user_id = ? AND is_active = TRUE AND expires_at > CURRENT_TIMESTAMP
            """
            result = self.db_manager.execute_query(query, (user_id,))
            stats['active_sessions'] = result[0]['count'] if result else 0
            
            # إجمالي الأنشطة
            query = """
                SELECT COUNT(*) as count FROM activity_logs 
                WHERE user_id = ?
            """
            result = self.db_manager.execute_query(query, (user_id,))
            stats['total_activities'] = result[0]['count'] if result else 0
            
            # الأحداث الأمنية
            query = """
                SELECT COUNT(*) as count FROM activity_logs 
                WHERE user_id = ? AND severity IN ('WARNING', 'ERROR', 'CRITICAL')
            """
            result = self.db_manager.execute_query(query, (user_id,))
            stats['security_events'] = result[0]['count'] if result else 0
            
            return stats
            
        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على إحصائيات المستخدم {user_id}: {e}")
            return {}
    
    def _get_user_recent_activities(self, user_id: int, limit: int = 20) -> List[Dict]:
        """
        الحصول على الأنشطة الأخيرة للمستخدم
        """
        try:
            query = """
                SELECT * FROM activity_logs 
                WHERE user_id = ? 
                ORDER BY created_at DESC 
                LIMIT ?
            """
            return self.db_manager.execute_query(query, (user_id, limit))
        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على أنشطة المستخدم {user_id}: {e}")
            return []
    
    def _get_user_active_sessions(self, user_id: int) -> List[Dict]:
        """
        الحصول على الجلسات النشطة للمستخدم
        """
        try:
            query = """
                SELECT * FROM sessions 
                WHERE user_id = ? AND is_active = TRUE AND expires_at > CURRENT_TIMESTAMP
                ORDER BY created_at DESC
            """
            return self.db_manager.execute_query(query, (user_id,))
        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على جلسات المستخدم {user_id}: {e}")
            return []
