# -*- coding: utf-8 -*-
"""
Cyber Shield - Cyper Member | State of Palestine
إعدادات التطبيق الرئيسية
"""

import os
from pathlib import Path

# معلومات التطبيق
APP_NAME = "Cyber Shield - Cyper Member"
APP_VERSION = "1.0.0"
APP_AUTHOR = "Cyper Member | State of Palestine"
APP_DESCRIPTION = "نظام حماية سيبرانية متقدم"

# مسارات المشروع
BASE_DIR = Path(__file__).parent.parent
SRC_DIR = BASE_DIR / "src"
ASSETS_DIR = BASE_DIR / "assets"
DATABASE_DIR = BASE_DIR / "database"
LOGS_DIR = BASE_DIR / "logs"
TEMP_DIR = BASE_DIR / "temp"

# إعدادات قاعدة البيانات
DATABASE_NAME = "cyber_shield.db"
DATABASE_PATH = DATABASE_DIR / DATABASE_NAME

# إعدادات الأمان
SECRET_KEY = "CyberShield_Palestine_2025_SecretKey"
ENCRYPTION_KEY_LENGTH = 32
PASSWORD_MIN_LENGTH = 8
SESSION_TIMEOUT = 3600  # ساعة واحدة

# بيانات المدير الافتراضي
DEFAULT_ADMIN_USERNAME = "admin"
DEFAULT_ADMIN_PASSWORD = "JaMaL@123"
DEFAULT_ADMIN_EMAIL = "<EMAIL>"
DEFAULT_ADMIN_FULL_NAME = "مدير النظام"

# إعدادات البريد الإلكتروني
SMTP_SERVER = "smtp.gmail.com"
SMTP_PORT = 587
EMAIL_SENDER = ""  # سيتم تعيينه من واجهة الإدارة
EMAIL_PASSWORD = ""  # سيتم تعيينه من واجهة الإدارة

# إعدادات التحقق بخطوتين
TWO_FACTOR_ENABLED = True
VERIFICATION_CODE_LENGTH = 6
VERIFICATION_CODE_EXPIRY = 300  # 5 دقائق

# إعدادات مراقبة النظام
SYSTEM_MONITORING_ENABLED = True
FILE_MONITORING_ENABLED = True
NETWORK_MONITORING_ENABLED = True

# إعدادات التنبيهات
ALERT_SOUND_ENABLED = True
EMAIL_ALERTS_ENABLED = True
DESKTOP_NOTIFICATIONS_ENABLED = True

# إعدادات تتبع الموقع
LOCATION_TRACKING_ENABLED = True
IP_GEOLOCATION_API = "http://ip-api.com/json/"

# إعدادات التحديث
AUTO_UPDATE_ENABLED = True
UPDATE_CHECK_INTERVAL = 86400  # 24 ساعة
UPDATE_SERVER_URL = "https://api.github.com/repos/cyper-member/cyber-shield"

# إعدادات الواجهة
THEME_MODE = "dark"
LANGUAGE = "ar"
WINDOW_WIDTH = 1200
WINDOW_HEIGHT = 800
WINDOW_MIN_WIDTH = 800
WINDOW_MIN_HEIGHT = 600

# ألوان التطبيق (علم فلسطين)
COLORS = {
    "primary": "#000000",      # أسود
    "secondary": "#FFFFFF",    # أبيض  
    "accent": "#FF0000",       # أحمر
    "success": "#00FF00",      # أخضر
    "warning": "#FFA500",      # برتقالي
    "danger": "#FF0000",       # أحمر
    "background": "#1a1a1a",   # خلفية داكنة
    "surface": "#2d2d2d",      # سطح
    "text": "#FFFFFF",         # نص أبيض
    "text_secondary": "#CCCCCC" # نص ثانوي
}

# مسارات الأيقونات والصور
ICONS = {
    "app_icon": ASSETS_DIR / "icons" / "app_icon.ico",
    "palestine_flag": ASSETS_DIR / "images" / "palestine_flag.png",
    "shield": ASSETS_DIR / "icons" / "shield.png",
    "lock": ASSETS_DIR / "icons" / "lock.png",
    "user": ASSETS_DIR / "icons" / "user.png",
    "admin": ASSETS_DIR / "icons" / "admin.png",
    "security": ASSETS_DIR / "icons" / "security.png",
    "location": ASSETS_DIR / "icons" / "location.png",
    "alert": ASSETS_DIR / "icons" / "alert.png"
}

# إعدادات السجلات
LOGGING_CONFIG = {
    "version": 1,
    "disable_existing_loggers": False,
    "formatters": {
        "standard": {
            "format": "%(asctime)s [%(levelname)s] %(name)s: %(message)s"
        },
    },
    "handlers": {
        "default": {
            "level": "INFO",
            "formatter": "standard",
            "class": "logging.FileHandler",
            "filename": LOGS_DIR / "app.log",
            "mode": "a",
        },
        "security": {
            "level": "WARNING",
            "formatter": "standard", 
            "class": "logging.FileHandler",
            "filename": LOGS_DIR / "security.log",
            "mode": "a",
        },
    },
    "loggers": {
        "": {
            "handlers": ["default"],
            "level": "INFO",
            "propagate": False
        },
        "security": {
            "handlers": ["security"],
            "level": "WARNING",
            "propagate": False
        }
    }
}

# إنشاء المجلدات إذا لم تكن موجودة
for directory in [ASSETS_DIR, DATABASE_DIR, LOGS_DIR, TEMP_DIR]:
    directory.mkdir(exist_ok=True)

# إنشاء مجلدات فرعية للأصول
(ASSETS_DIR / "icons").mkdir(exist_ok=True)
(ASSETS_DIR / "images").mkdir(exist_ok=True)
(ASSETS_DIR / "sounds").mkdir(exist_ok=True)
