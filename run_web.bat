@echo off
chcp 65001 > nul
title Cyber Shield Web App - Cyper Member | State of Palestine

echo.
echo ===============================================
echo 🇵🇸 Cyber Shield Web App - Cyper Member 🇵🇸
echo State of Palestine
echo ===============================================
echo.

echo 🔍 التحقق من Python...
python --version > nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت أو غير موجود في PATH
    echo يرجى تثبيت Python 3.8 أو أحدث من: https://python.org
    pause
    exit /b 1
)

echo ✅ تم العثور على Python

echo.
echo 📦 تثبيت المتطلبات...
python -m pip install -r requirements.txt

if errorlevel 1 (
    echo ❌ فشل في تثبيت المتطلبات
    pause
    exit /b 1
)

echo ✅ تم تثبيت المتطلبات بنجاح

echo.
echo 🌐 تشغيل Cyber Shield Web App...
echo 📍 الرابط: http://localhost:5000
echo 🔐 المستخدم: admin | كلمة المرور: JaMaL@123
echo.

python web_app.py

if errorlevel 1 (
    echo.
    echo ❌ حدث خطأ في تشغيل التطبيق
    pause
    exit /b 1
)

echo.
echo ✅ تم إغلاق التطبيق بنجاح
pause
