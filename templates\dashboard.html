{% extends "base.html" %}

{% block title %}لوحة المعلومات - Cyber Shield{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-tachometer-alt text-primary me-2"></i>
                        لوحة المعلومات
                    </h1>
                    <p class="text-muted mb-0">مرحباً {{ user.full_name or user.username }}</p>
                </div>
                <div>
                    <span class="badge bg-success fs-6">
                        <i class="fas fa-circle me-1"></i>
                        متصل
                    </span>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-primary bg-gradient rounded-circle p-3">
                                <i class="fas fa-shield-alt text-white fa-lg"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <div class="small text-muted">حالة الحماية</div>
                            <div class="h5 mb-0">
                                {% if dashboard.overview.protection_status %}
                                    <span class="text-success">نشطة</span>
                                {% else %}
                                    <span class="text-danger">متوقفة</span>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-warning bg-gradient rounded-circle p-3">
                                <i class="fas fa-exclamation-triangle text-white fa-lg"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <div class="small text-muted">التهديدات اليوم</div>
                            <div class="h5 mb-0">{{ dashboard.overview.threats_today or 0 }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-info bg-gradient rounded-circle p-3">
                                <i class="fas fa-users text-white fa-lg"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <div class="small text-muted">الجلسات النشطة</div>
                            <div class="h5 mb-0">{{ dashboard.overview.active_sessions or 0 }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-success bg-gradient rounded-circle p-3">
                                <i class="fas fa-heartbeat text-white fa-lg"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <div class="small text-muted">صحة النظام</div>
                            <div class="h5 mb-0">
                                <span class="text-success">جيدة</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Main Content Row -->
    <div class="row">
        <!-- Security Status -->
        <div class="col-lg-8 mb-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-transparent border-0 pb-0">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-shield-virus text-primary me-2"></i>
                            حالة الأمان
                        </h5>
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-outline-success btn-sm" id="startProtection">
                                <i class="fas fa-play me-1"></i>بدء الحماية
                            </button>
                            <button type="button" class="btn btn-outline-danger btn-sm" id="stopProtection">
                                <i class="fas fa-stop me-1"></i>إيقاف الحماية
                            </button>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label small text-muted">مستوى الأمان</label>
                                <div class="progress" style="height: 8px;">
                                    <div class="progress-bar bg-success" style="width: 85%"></div>
                                </div>
                                <small class="text-muted">عالي (85%)</small>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label small text-muted">مراقبة الملفات</label>
                                <div class="d-flex align-items-center">
                                    <span class="badge bg-success me-2">نشطة</span>
                                    <small class="text-muted">{{ dashboard.overview.total_threats or 0 }} ملف مراقب</small>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label small text-muted">مراقبة الشبكة</label>
                                <div class="d-flex align-items-center">
                                    <span class="badge bg-success me-2">نشطة</span>
                                    <small class="text-muted">{{ dashboard.network_status.total_connections or 0 }} اتصال</small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="text-center">
                                <div class="position-relative d-inline-block">
                                    <canvas id="securityChart" width="150" height="150"></canvas>
                                    <div class="position-absolute top-50 start-50 translate-middle text-center">
                                        <div class="h4 mb-0 text-success">85%</div>
                                        <small class="text-muted">آمن</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Quick Actions -->
                    <div class="mt-3 pt-3 border-top">
                        <div class="row g-2">
                            <div class="col-md-3">
                                <button class="btn btn-outline-primary btn-sm w-100" id="quickScan">
                                    <i class="fas fa-search me-1"></i>فحص سريع
                                </button>
                            </div>
                            <div class="col-md-3">
                                <button class="btn btn-outline-info btn-sm w-100" id="fullScan">
                                    <i class="fas fa-search-plus me-1"></i>فحص شامل
                                </button>
                            </div>
                            <div class="col-md-3">
                                <button class="btn btn-outline-warning btn-sm w-100" id="updateDefinitions">
                                    <i class="fas fa-download me-1"></i>تحديث التعريفات
                                </button>
                            </div>
                            <div class="col-md-3">
                                <button class="btn btn-outline-secondary btn-sm w-100" id="viewLogs">
                                    <i class="fas fa-list me-1"></i>عرض السجلات
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Recent Activities -->
        <div class="col-lg-4 mb-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-transparent border-0 pb-0">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-history text-info me-2"></i>
                        الأنشطة الأخيرة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="activity-list" style="max-height: 300px; overflow-y: auto;">
                        {% if dashboard.recent_activities %}
                            {% for activity in dashboard.recent_activities[:10] %}
                            <div class="d-flex align-items-start mb-3">
                                <div class="flex-shrink-0">
                                    <div class="bg-{{ 'danger' if activity.severity == 'ERROR' else 'warning' if activity.severity == 'WARNING' else 'info' }} rounded-circle p-2">
                                        <i class="fas fa-{{ 'exclamation-triangle' if activity.severity == 'ERROR' else 'exclamation-circle' if activity.severity == 'WARNING' else 'info-circle' }} text-white fa-sm"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <div class="small fw-bold">{{ activity.action }}</div>
                                    <div class="small text-muted">{{ activity.description }}</div>
                                    <div class="small text-muted">
                                        <i class="fas fa-clock me-1"></i>
                                        {{ activity.created_at }}
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        {% else %}
                            <div class="text-center text-muted py-4">
                                <i class="fas fa-inbox fa-2x mb-2"></i>
                                <p>لا توجد أنشطة حديثة</p>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Security Alerts -->
    {% if dashboard.security_alerts %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-transparent border-0">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                        التنبيهات الأمنية
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% for alert in dashboard.security_alerts[:6] %}
                        <div class="col-md-6 col-lg-4 mb-3">
                            <div class="alert alert-{{ 'danger' if alert.severity == 'CRITICAL' else 'warning' if alert.severity == 'HIGH' else 'info' }} mb-0">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-shield-alt me-2"></i>
                                    <div>
                                        <div class="fw-bold">{{ alert.threat_type }}</div>
                                        <small>{{ alert.detected_at }}</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
    
    <!-- System Performance -->
    <div class="row">
        <div class="col-lg-6 mb-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-transparent border-0">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-line text-success me-2"></i>
                        أداء النظام
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="performanceChart" height="200"></canvas>
                </div>
            </div>
        </div>
        
        <div class="col-lg-6 mb-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-transparent border-0">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-globe text-primary me-2"></i>
                        إحصائيات الشبكة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-4">
                            <div class="h4 text-primary mb-0">{{ dashboard.network_status.total_connections or 0 }}</div>
                            <small class="text-muted">إجمالي الاتصالات</small>
                        </div>
                        <div class="col-4">
                            <div class="h4 text-warning mb-0">{{ dashboard.network_status.suspicious_connections or 0 }}</div>
                            <small class="text-muted">اتصالات مشبوهة</small>
                        </div>
                        <div class="col-4">
                            <div class="h4 text-info mb-0">{{ dashboard.network_status.unique_ips or 0 }}</div>
                            <small class="text-muted">عناوين IP فريدة</small>
                        </div>
                    </div>
                    
                    <hr class="my-3">
                    
                    <div class="d-flex justify-content-between align-items-center">
                        <span class="small text-muted">حركة البيانات</span>
                        <button class="btn btn-outline-primary btn-sm" onclick="window.location.href='/location'">
                            <i class="fas fa-map-marked-alt me-1"></i>عرض الخريطة
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Scan Progress Modal -->
<div class="modal fade" id="scanModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-search me-2"></i>
                    جاري الفحص...
                </h5>
            </div>
            <div class="modal-body">
                <div class="progress mb-3" style="height: 20px;">
                    <div class="progress-bar progress-bar-striped progress-bar-animated" 
                         id="scanProgress" style="width: 0%"></div>
                </div>
                <div class="text-center">
                    <div id="scanStatus">بدء الفحص...</div>
                    <small class="text-muted" id="scanDetails">جاري تحضير الفحص</small>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
    // إعداد الرسوم البيانية
    document.addEventListener('DOMContentLoaded', function() {
        // رسم بياني دائري للأمان
        const securityCtx = document.getElementById('securityChart').getContext('2d');
        new Chart(securityCtx, {
            type: 'doughnut',
            data: {
                datasets: [{
                    data: [85, 15],
                    backgroundColor: ['#28a745', '#e9ecef'],
                    borderWidth: 0
                }]
            },
            options: {
                responsive: false,
                maintainAspectRatio: false,
                cutout: '70%',
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });
        
        // رسم بياني للأداء
        const performanceCtx = document.getElementById('performanceChart').getContext('2d');
        new Chart(performanceCtx, {
            type: 'line',
            data: {
                labels: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00'],
                datasets: [{
                    label: 'استخدام المعالج',
                    data: [20, 35, 45, 60, 40, 30],
                    borderColor: '#007bff',
                    backgroundColor: 'rgba(0, 123, 255, 0.1)',
                    tension: 0.4
                }, {
                    label: 'استخدام الذاكرة',
                    data: [30, 40, 35, 50, 45, 35],
                    borderColor: '#28a745',
                    backgroundColor: 'rgba(40, 167, 69, 0.1)',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100,
                        ticks: {
                            callback: function(value) {
                                return value + '%';
                            }
                        }
                    }
                }
            }
        });
    });
    
    // معالجات الأحداث
    document.getElementById('startProtection').addEventListener('click', async function() {
        try {
            showLoading(true);
            const response = await fetch('/api/security/start', { method: 'POST' });
            const result = await response.json();
            
            if (result.success) {
                showNotification(result.message, 'success');
                setTimeout(() => location.reload(), 1000);
            } else {
                showNotification(result.message, 'error');
            }
        } catch (error) {
            showNotification('حدث خطأ في بدء الحماية', 'error');
        } finally {
            showLoading(false);
        }
    });
    
    document.getElementById('stopProtection').addEventListener('click', async function() {
        if (confirm('هل أنت متأكد من إيقاف الحماية؟')) {
            try {
                showLoading(true);
                const response = await fetch('/api/security/stop', { method: 'POST' });
                const result = await response.json();
                
                if (result.success) {
                    showNotification(result.message, 'success');
                    setTimeout(() => location.reload(), 1000);
                } else {
                    showNotification(result.message, 'error');
                }
            } catch (error) {
                showNotification('حدث خطأ في إيقاف الحماية', 'error');
            } finally {
                showLoading(false);
            }
        }
    });
    
    document.getElementById('quickScan').addEventListener('click', async function() {
        const modal = new bootstrap.Modal(document.getElementById('scanModal'));
        modal.show();
        
        try {
            const response = await fetch('/api/scan/quick', { method: 'POST' });
            const result = await response.json();
            
            if (result.success) {
                showNotification('تم بدء الفحص السريع', 'info');
            } else {
                showNotification(result.message, 'error');
                modal.hide();
            }
        } catch (error) {
            showNotification('حدث خطأ في بدء الفحص', 'error');
            modal.hide();
        }
    });
    
    // تحديث تقدم الفحص
    function updateScanProgress(progress) {
        const progressBar = document.getElementById('scanProgress');
        const status = document.getElementById('scanStatus');
        const details = document.getElementById('scanDetails');
        
        progressBar.style.width = progress + '%';
        status.textContent = `تقدم الفحص: ${progress}%`;
        
        if (progress >= 100) {
            details.textContent = 'تم الانتهاء من الفحص';
            setTimeout(() => {
                bootstrap.Modal.getInstance(document.getElementById('scanModal')).hide();
                showNotification('تم الانتهاء من الفحص بنجاح', 'success');
            }, 1000);
        } else {
            details.textContent = 'جاري فحص الملفات...';
        }
    }
</script>
{% endblock %}
