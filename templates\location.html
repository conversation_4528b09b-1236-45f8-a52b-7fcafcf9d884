{% extends "base.html" %}

{% block title %}المواقع - Cyber Shield{% endblock %}

{% block extra_head %}
<!-- Leaflet CSS -->
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-map-marked-alt text-primary me-2"></i>
                        تتبع المواقع الجغرافية
                    </h1>
                    <p class="text-muted mb-0">مراقبة وتحليل مواقع الاتصالات والتهديدات</p>
                </div>
                <div>
                    <button class="btn btn-primary" id="refreshLocation">
                        <i class="fas fa-sync-alt me-1"></i>
                        تحديث
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Current Location -->
    <div class="row mb-4">
        <div class="col-lg-6">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-transparent border-0">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-map-pin text-success me-2"></i>
                        موقعك الحالي
                    </h5>
                </div>
                <div class="card-body">
                    <div id="currentLocationInfo">
                        <div class="text-center py-3">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">جاري التحميل...</span>
                            </div>
                            <p class="mt-2 text-muted">جاري تحديد الموقع...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-6">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-transparent border-0">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-bar text-info me-2"></i>
                        إحصائيات المواقع
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-4">
                            <div class="h4 text-primary mb-0">{{ stats.total_ips or 0 }}</div>
                            <small class="text-muted">عناوين IP</small>
                        </div>
                        <div class="col-4">
                            <div class="h4 text-warning mb-0">{{ stats.suspicious_ips or 0 }}</div>
                            <small class="text-muted">IP مشبوه</small>
                        </div>
                        <div class="col-4">
                            <div class="h4 text-success mb-0">{{ stats.countries or 0 }}</div>
                            <small class="text-muted">دولة</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Map Controls -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-transparent border-0">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-globe text-primary me-2"></i>
                            الخريطة التفاعلية
                        </h5>
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-outline-primary btn-sm" id="showConnections">
                                <i class="fas fa-network-wired me-1"></i>الاتصالات
                            </button>
                            <button type="button" class="btn btn-outline-warning btn-sm" id="showThreats">
                                <i class="fas fa-exclamation-triangle me-1"></i>التهديدات
                            </button>
                            <button type="button" class="btn btn-outline-info btn-sm" id="showLive">
                                <i class="fas fa-broadcast-tower me-1"></i>مباشر
                            </button>
                        </div>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div id="map" style="height: 500px; width: 100%;"></div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- IP Analysis -->
    <div class="row mb-4">
        <div class="col-lg-8">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-transparent border-0">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-search text-primary me-2"></i>
                            تحليل عنوان IP
                        </h5>
                    </div>
                </div>
                <div class="card-body">
                    <div class="input-group mb-3">
                        <input type="text" class="form-control" id="ipInput" 
                               placeholder="أدخل عنوان IP للتحليل (مثال: *******)">
                        <button class="btn btn-primary" id="analyzeIP">
                            <i class="fas fa-search me-1"></i>تحليل
                        </button>
                    </div>
                    
                    <div id="ipAnalysisResult" class="d-none">
                        <div class="row">
                            <div class="col-md-6">
                                <table class="table table-sm">
                                    <tr>
                                        <td><strong>عنوان IP:</strong></td>
                                        <td id="resultIP">-</td>
                                    </tr>
                                    <tr>
                                        <td><strong>الدولة:</strong></td>
                                        <td id="resultCountry">-</td>
                                    </tr>
                                    <tr>
                                        <td><strong>المدينة:</strong></td>
                                        <td id="resultCity">-</td>
                                    </tr>
                                    <tr>
                                        <td><strong>مقدم الخدمة:</strong></td>
                                        <td id="resultISP">-</td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <table class="table table-sm">
                                    <tr>
                                        <td><strong>خط الطول:</strong></td>
                                        <td id="resultLon">-</td>
                                    </tr>
                                    <tr>
                                        <td><strong>خط العرض:</strong></td>
                                        <td id="resultLat">-</td>
                                    </tr>
                                    <tr>
                                        <td><strong>المنطقة الزمنية:</strong></td>
                                        <td id="resultTimezone">-</td>
                                    </tr>
                                    <tr>
                                        <td><strong>حالة الأمان:</strong></td>
                                        <td id="resultSecurity">-</td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-transparent border-0">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list text-primary me-2"></i>
                        أحدث المواقع
                    </h5>
                </div>
                <div class="card-body">
                    <div id="recentLocations" style="max-height: 300px; overflow-y: auto;">
                        <!-- سيتم ملؤها بـ JavaScript -->
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Country Statistics -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-transparent border-0">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-flag text-primary me-2"></i>
                        إحصائيات الدول
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-lg-8">
                            <canvas id="countriesChart" height="200"></canvas>
                        </div>
                        <div class="col-lg-4">
                            <div id="countriesList">
                                <!-- سيتم ملؤها بـ JavaScript -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<!-- Leaflet JS -->
<script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>

<script>
    let map;
    let markersLayer;
    let currentLocationMarker;
    
    // تهيئة الخريطة
    document.addEventListener('DOMContentLoaded', function() {
        initializeMap();
        loadCurrentLocation();
        loadRecentLocations();
        loadCountriesChart();
        
        // معالجات الأحداث
        document.getElementById('refreshLocation').addEventListener('click', refreshAllData);
        document.getElementById('analyzeIP').addEventListener('click', analyzeIP);
        document.getElementById('showConnections').addEventListener('click', () => showMapData('connections'));
        document.getElementById('showThreats').addEventListener('click', () => showMapData('threats'));
        document.getElementById('showLive').addEventListener('click', () => showMapData('live'));
        
        // تحليل IP عند الضغط على Enter
        document.getElementById('ipInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                analyzeIP();
            }
        });
    });
    
    function initializeMap() {
        // إنشاء الخريطة مع التركيز على فلسطين
        map = L.map('map').setView([31.9522, 35.2332], 8);
        
        // إضافة طبقة الخريطة
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '© OpenStreetMap contributors'
        }).addTo(map);
        
        // إنشاء طبقة للعلامات
        markersLayer = L.layerGroup().addTo(map);
        
        // إضافة علامة فلسطين
        const palestineMarker = L.marker([31.9522, 35.2332])
            .bindPopup('<div class="text-center"><h6>🇵🇸 فلسطين</h6><p>Cyper Member | State of Palestine</p></div>')
            .addTo(map);
    }
    
    async function loadCurrentLocation() {
        try {
            const response = await fetch('/api/location/current');
            const result = await response.json();
            
            if (result.success && result.data) {
                displayCurrentLocation(result.data);
            } else {
                displayLocationError();
            }
        } catch (error) {
            console.error('خطأ في تحميل الموقع الحالي:', error);
            displayLocationError();
        }
    }
    
    function displayCurrentLocation(location) {
        const container = document.getElementById('currentLocationInfo');
        
        const html = `
            <div class="row">
                <div class="col-6">
                    <div class="mb-2">
                        <strong>عنوان IP:</strong><br>
                        <span class="text-primary">${location.ip || 'غير متاح'}</span>
                    </div>
                    <div class="mb-2">
                        <strong>الدولة:</strong><br>
                        <span>${location.country || 'غير متاح'}</span>
                    </div>
                    <div class="mb-2">
                        <strong>المدينة:</strong><br>
                        <span>${location.city || 'غير متاح'}</span>
                    </div>
                </div>
                <div class="col-6">
                    <div class="mb-2">
                        <strong>مقدم الخدمة:</strong><br>
                        <span>${location.isp || 'غير متاح'}</span>
                    </div>
                    <div class="mb-2">
                        <strong>الإحداثيات:</strong><br>
                        <span>${location.lat || 0}, ${location.lon || 0}</span>
                    </div>
                    <div class="mb-2">
                        <strong>المنطقة الزمنية:</strong><br>
                        <span>${location.timezone || 'غير متاح'}</span>
                    </div>
                </div>
            </div>
        `;
        
        container.innerHTML = html;
        
        // إضافة علامة على الخريطة
        if (location.lat && location.lon) {
            if (currentLocationMarker) {
                map.removeLayer(currentLocationMarker);
            }
            
            currentLocationMarker = L.marker([location.lat, location.lon])
                .bindPopup(`<div class="text-center"><h6>موقعك الحالي</h6><p>${location.city}, ${location.country}</p></div>`)
                .addTo(map);
        }
    }
    
    function displayLocationError() {
        const container = document.getElementById('currentLocationInfo');
        container.innerHTML = `
            <div class="text-center text-muted py-3">
                <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                <p>لا يمكن تحديد الموقع الحالي</p>
            </div>
        `;
    }
    
    async function analyzeIP() {
        const ipInput = document.getElementById('ipInput');
        const ip = ipInput.value.trim();
        
        if (!ip) {
            showNotification('يرجى إدخال عنوان IP', 'warning');
            return;
        }
        
        // التحقق من صحة عنوان IP
        const ipRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
        if (!ipRegex.test(ip)) {
            showNotification('عنوان IP غير صحيح', 'error');
            return;
        }
        
        try {
            showLoading(true);
            
            const response = await fetch(`/api/location/ip/${ip}`);
            const result = await response.json();
            
            if (result.success && result.data) {
                displayIPAnalysis(result.data);
                
                // إضافة علامة على الخريطة
                if (result.data.lat && result.data.lon) {
                    const marker = L.marker([result.data.lat, result.data.lon])
                        .bindPopup(`
                            <div class="text-center">
                                <h6>${result.data.ip}</h6>
                                <p>${result.data.city}, ${result.data.country}</p>
                                <small>${result.data.isp}</small>
                            </div>
                        `)
                        .addTo(markersLayer);
                    
                    map.setView([result.data.lat, result.data.lon], 10);
                }
            } else {
                showNotification('فشل في تحليل عنوان IP', 'error');
            }
        } catch (error) {
            showNotification('حدث خطأ في تحليل عنوان IP', 'error');
        } finally {
            showLoading(false);
        }
    }
    
    function displayIPAnalysis(data) {
        document.getElementById('resultIP').textContent = data.ip || '-';
        document.getElementById('resultCountry').textContent = data.country || '-';
        document.getElementById('resultCity').textContent = data.city || '-';
        document.getElementById('resultISP').textContent = data.isp || '-';
        document.getElementById('resultLon').textContent = data.lon || '-';
        document.getElementById('resultLat').textContent = data.lat || '-';
        document.getElementById('resultTimezone').textContent = data.timezone || '-';
        
        // تحديد حالة الأمان
        const securityElement = document.getElementById('resultSecurity');
        if (data.is_suspicious) {
            securityElement.innerHTML = '<span class="badge bg-danger">مشبوه</span>';
        } else {
            securityElement.innerHTML = '<span class="badge bg-success">آمن</span>';
        }
        
        document.getElementById('ipAnalysisResult').classList.remove('d-none');
    }
    
    async function showMapData(type) {
        try {
            showLoading(true);
            
            const response = await fetch(`/api/map/generate`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ type: type, limit: 100 })
            });
            
            const result = await response.json();
            
            if (result.success && result.data) {
                // مسح العلامات السابقة
                markersLayer.clearLayers();
                
                // إضافة العلامات الجديدة
                if (result.data.locations) {
                    result.data.locations.forEach(location => {
                        if (location.lat && location.lon) {
                            const color = type === 'threats' ? 'red' : type === 'connections' ? 'blue' : 'green';
                            
                            const marker = L.circleMarker([location.lat, location.lon], {
                                color: color,
                                fillColor: color,
                                fillOpacity: 0.6,
                                radius: 8
                            }).bindPopup(`
                                <div class="text-center">
                                    <h6>${location.ip}</h6>
                                    <p>${location.city}, ${location.country}</p>
                                    ${type === 'threats' ? `<span class="badge bg-danger">${location.threat_type}</span>` : ''}
                                </div>
                            `).addTo(markersLayer);
                        }
                    });
                }
                
                showNotification(`تم تحميل خريطة ${type === 'threats' ? 'التهديدات' : type === 'connections' ? 'الاتصالات' : 'المباشرة'}`, 'success');
            } else {
                showNotification('فشل في تحميل بيانات الخريطة', 'error');
            }
        } catch (error) {
            showNotification('حدث خطأ في تحميل الخريطة', 'error');
        } finally {
            showLoading(false);
        }
    }
    
    async function loadRecentLocations() {
        try {
            const response = await fetch('/api/location/history?limit=10');
            const result = await response.json();
            
            if (result.success && result.data) {
                displayRecentLocations(result.data);
            }
        } catch (error) {
            console.error('خطأ في تحميل المواقع الأخيرة:', error);
        }
    }
    
    function displayRecentLocations(locations) {
        const container = document.getElementById('recentLocations');
        
        if (!locations || locations.length === 0) {
            container.innerHTML = '<div class="text-center text-muted py-3">لا توجد مواقع حديثة</div>';
            return;
        }
        
        const html = locations.map(location => `
            <div class="d-flex align-items-center mb-2 p-2 bg-light rounded">
                <div class="flex-shrink-0">
                    <i class="fas fa-map-pin text-primary"></i>
                </div>
                <div class="flex-grow-1 ms-2">
                    <div class="small fw-bold">${location.ip}</div>
                    <div class="small text-muted">${location.city}, ${location.country}</div>
                </div>
                <div class="flex-shrink-0">
                    <small class="text-muted">${formatDate(location.detected_at)}</small>
                </div>
            </div>
        `).join('');
        
        container.innerHTML = html;
    }
    
    async function loadCountriesChart() {
        try {
            const response = await fetch('/api/location/countries-stats');
            const result = await response.json();
            
            if (result.success && result.data) {
                displayCountriesChart(result.data);
            }
        } catch (error) {
            console.error('خطأ في تحميل إحصائيات الدول:', error);
        }
    }
    
    function displayCountriesChart(data) {
        const ctx = document.getElementById('countriesChart').getContext('2d');
        
        new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: data.countries || [],
                datasets: [{
                    data: data.counts || [],
                    backgroundColor: [
                        '#007a3d', '#ce1126', '#000000', '#ffffff',
                        '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0'
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
        
        // عرض قائمة الدول
        const listContainer = document.getElementById('countriesList');
        if (data.countries && data.counts) {
            const html = data.countries.map((country, index) => `
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span>${country}</span>
                    <span class="badge bg-primary">${data.counts[index]}</span>
                </div>
            `).join('');
            
            listContainer.innerHTML = html;
        }
    }
    
    async function refreshAllData() {
        showLoading(true);
        
        try {
            await Promise.all([
                loadCurrentLocation(),
                loadRecentLocations(),
                loadCountriesChart()
            ]);
            
            showNotification('تم تحديث جميع البيانات', 'success');
        } catch (error) {
            showNotification('حدث خطأ في تحديث البيانات', 'error');
        } finally {
            showLoading(false);
        }
    }
    
    function formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('ar-SA', {
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    }
</script>
{% endblock %}
