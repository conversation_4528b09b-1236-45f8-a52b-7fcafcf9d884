# -*- coding: utf-8 -*-
"""
Cyber Shield - Build EXE Script
سكريبت بناء ملف EXE للتطبيق
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

# إضافة مسار المشروع
sys.path.insert(0, str(Path(__file__).parent))
from config.settings import APP_NAME, APP_VERSION, APP_AUTHOR

def install_requirements():
    """تثبيت المتطلبات"""
    print("📦 تثبيت المتطلبات...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
        print("✅ تم تثبيت المتطلبات بنجاح")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ خطأ في تثبيت المتطلبات: {e}")
        return False

def create_spec_file():
    """إنشاء ملف spec لـ PyInstaller"""
    spec_content = f'''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('config', 'config'),
        ('assets', 'assets'),
        ('database/schema.sql', 'database'),
        ('requirements.txt', '.'),
        ('README.md', '.'),
    ],
    hiddenimports=[
        'tkinter',
        'customtkinter',
        'PIL',
        'requests',
        'cryptography',
        'bcrypt',
        'psutil',
        'sqlite3',
        'json',
        'datetime',
        'threading',
        'logging',
        'pathlib',
        'hashlib',
        'socket',
        'smtplib',
        'email',
        'zipfile',
        'shutil',
        'tempfile',
        'uuid',
        'base64',
        'os',
        'sys',
        'time',
        're',
        'csv',
        'ipaddress',
        'urllib',
        'webbrowser',
    ],
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='{APP_NAME.replace(" ", "_")}',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='assets/icon.ico' if os.path.exists('assets/icon.ico') else None,
    version='version_info.txt' if os.path.exists('version_info.txt') else None,
)
'''
    
    with open('cyber_shield.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ تم إنشاء ملف spec")

def create_version_info():
    """إنشاء ملف معلومات الإصدار"""
    version_parts = APP_VERSION.split('.')
    while len(version_parts) < 4:
        version_parts.append('0')
    
    version_info = f'''# UTF-8
#
# For more details about fixed file info 'ffi' see:
# http://msdn.microsoft.com/en-us/library/ms646997.aspx
VSVersionInfo(
  ffi=FixedFileInfo(
    filevers=({version_parts[0]},{version_parts[1]},{version_parts[2]},{version_parts[3]}),
    prodvers=({version_parts[0]},{version_parts[1]},{version_parts[2]},{version_parts[3]}),
    mask=0x3f,
    flags=0x0,
    OS=0x40004,
    fileType=0x1,
    subtype=0x0,
    date=(0, 0)
    ),
  kids=[
    StringFileInfo(
      [
      StringTable(
        u'040904B0',
        [StringStruct(u'CompanyName', u'{APP_AUTHOR}'),
        StringStruct(u'FileDescription', u'{APP_NAME} - Cybersecurity Application'),
        StringStruct(u'FileVersion', u'{APP_VERSION}'),
        StringStruct(u'InternalName', u'{APP_NAME.replace(" ", "_")}'),
        StringStruct(u'LegalCopyright', u'© 2025 {APP_AUTHOR}'),
        StringStruct(u'OriginalFilename', u'{APP_NAME.replace(" ", "_")}.exe'),
        StringStruct(u'ProductName', u'{APP_NAME}'),
        StringStruct(u'ProductVersion', u'{APP_VERSION}')])
      ]), 
    VarFileInfo([VarStruct(u'Translation', [1033, 1200])])
  ]
)
'''
    
    with open('version_info.txt', 'w', encoding='utf-8') as f:
        f.write(version_info)
    
    print("✅ تم إنشاء ملف معلومات الإصدار")

def create_icon():
    """إنشاء أيقونة التطبيق"""
    assets_dir = Path("assets")
    assets_dir.mkdir(exist_ok=True)
    
    # إنشاء أيقونة بسيطة إذا لم تكن موجودة
    icon_path = assets_dir / "icon.ico"
    if not icon_path.exists():
        try:
            from PIL import Image, ImageDraw
            
            # إنشاء صورة 256x256
            img = Image.new('RGBA', (256, 256), (0, 0, 0, 0))
            draw = ImageDraw.Draw(img)
            
            # رسم درع بسيط
            # الخلفية
            draw.ellipse([20, 20, 236, 236], fill=(0, 100, 200, 255))
            
            # الدرع
            shield_points = [
                (128, 40),   # أعلى
                (200, 80),   # يمين أعلى
                (200, 160),  # يمين أسفل
                (128, 220),  # أسفل
                (56, 160),   # يسار أسفل
                (56, 80),    # يسار أعلى
            ]
            draw.polygon(shield_points, fill=(255, 255, 255, 255))
            
            # رمز الحماية
            draw.ellipse([100, 100, 156, 156], fill=(0, 200, 0, 255))
            draw.text((118, 118), "✓", fill=(255, 255, 255, 255))
            
            # حفظ كأيقونة
            img.save(icon_path, format='ICO', sizes=[(256, 256), (128, 128), (64, 64), (32, 32), (16, 16)])
            print("✅ تم إنشاء أيقونة التطبيق")
            
        except ImportError:
            print("⚠️ لا يمكن إنشاء الأيقونة - Pillow غير مثبت")
        except Exception as e:
            print(f"⚠️ خطأ في إنشاء الأيقونة: {e}")

def build_exe():
    """بناء ملف EXE"""
    print("🔨 بناء ملف EXE...")
    
    try:
        # تنظيف الملفات السابقة
        if Path("dist").exists():
            shutil.rmtree("dist")
        if Path("build").exists():
            shutil.rmtree("build")
        
        # بناء التطبيق
        cmd = [
            sys.executable, "-m", "PyInstaller",
            "--clean",
            "--noconfirm",
            "cyber_shield.spec"
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ تم بناء ملف EXE بنجاح")
            
            # نسخ الملفات الإضافية
            dist_dir = Path("dist")
            if dist_dir.exists():
                # نسخ قاعدة البيانات
                db_dir = dist_dir / "database"
                db_dir.mkdir(exist_ok=True)
                if Path("database/schema.sql").exists():
                    shutil.copy2("database/schema.sql", db_dir)
                
                # نسخ الأصول
                if Path("assets").exists():
                    shutil.copytree("assets", dist_dir / "assets", dirs_exist_ok=True)
                
                print(f"📁 ملف EXE متاح في: {dist_dir.absolute()}")
                return True
        else:
            print(f"❌ خطأ في بناء EXE: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في بناء EXE: {e}")
        return False

def create_installer():
    """إنشاء ملف تثبيت"""
    print("📦 إنشاء ملف التثبيت...")
    
    # إنشاء سكريبت Inno Setup
    iss_content = f'''[Setup]
AppName={APP_NAME}
AppVersion={APP_VERSION}
AppPublisher={APP_AUTHOR}
AppPublisherURL=https://github.com/cyper-member
AppSupportURL=https://github.com/cyper-member/cyber-shield/issues
AppUpdatesURL=https://github.com/cyper-member/cyber-shield/releases
DefaultDirName={{autopf}}\\{APP_NAME}
DefaultGroupName={APP_NAME}
AllowNoIcons=yes
LicenseFile=LICENSE
OutputDir=installer
OutputBaseFilename={APP_NAME.replace(" ", "_")}_Setup_v{APP_VERSION}
SetupIconFile=assets\\icon.ico
Compression=lzma
SolidCompression=yes
WizardStyle=modern
ArchitecturesAllowed=x64
ArchitecturesInstallIn64BitMode=x64

[Languages]
Name: "arabic"; MessagesFile: "compiler:Languages\\Arabic.isl"
Name: "english"; MessagesFile: "compiler:Default.isl"

[Tasks]
Name: "desktopicon"; Description: "{{cm:CreateDesktopIcon}}"; GroupDescription: "{{cm:AdditionalIcons}}"; Flags: unchecked
Name: "quicklaunchicon"; Description: "{{cm:CreateQuickLaunchIcon}}"; GroupDescription: "{{cm:AdditionalIcons}}"; Flags: unchecked; OnlyBelowVersion: 6.1

[Files]
Source: "dist\\{APP_NAME.replace(" ", "_")}.exe"; DestDir: "{{app}}"; Flags: ignoreversion
Source: "dist\\*"; DestDir: "{{app}}"; Flags: ignoreversion recursesubdirs createallsubdirs

[Icons]
Name: "{{group}}\\{APP_NAME}"; Filename: "{{app}}\\{APP_NAME.replace(" ", "_")}.exe"
Name: "{{group}}\\{{cm:UninstallProgram,{APP_NAME}}}"; Filename: "{{uninstallexe}}"
Name: "{{autodesktop}}\\{APP_NAME}"; Filename: "{{app}}\\{APP_NAME.replace(" ", "_")}.exe"; Tasks: desktopicon
Name: "{{userappdata}}\\Microsoft\\Internet Explorer\\Quick Launch\\{APP_NAME}"; Filename: "{{app}}\\{APP_NAME.replace(" ", "_")}.exe"; Tasks: quicklaunchicon

[Run]
Filename: "{{app}}\\{APP_NAME.replace(" ", "_")}.exe"; Description: "{{cm:LaunchProgram,{APP_NAME}}}"; Flags: nowait postinstall skipifsilent
'''
    
    with open('cyber_shield_setup.iss', 'w', encoding='utf-8') as f:
        f.write(iss_content)
    
    print("✅ تم إنشاء سكريبت التثبيت")
    print("💡 لإنشاء ملف التثبيت، استخدم Inno Setup مع الملف: cyber_shield_setup.iss")

def main():
    """الدالة الرئيسية"""
    print(f"🚀 بناء {APP_NAME} v{APP_VERSION}")
    print("=" * 50)
    
    # التحقق من Python
    if sys.version_info < (3, 8):
        print("❌ يتطلب Python 3.8 أو أحدث")
        return False
    
    # تثبيت المتطلبات
    if not install_requirements():
        return False
    
    # إنشاء الملفات المطلوبة
    create_icon()
    create_version_info()
    create_spec_file()
    
    # بناء EXE
    if build_exe():
        create_installer()
        print("\n✅ تم بناء التطبيق بنجاح!")
        print(f"📁 ملف EXE: dist/{APP_NAME.replace(' ', '_')}.exe")
        print(f"📦 سكريبت التثبيت: cyber_shield_setup.iss")
        return True
    else:
        print("\n❌ فشل في بناء التطبيق")
        return False

if __name__ == "__main__":
    success = main()
    input("\nاضغط Enter للخروج...")
    sys.exit(0 if success else 1)
