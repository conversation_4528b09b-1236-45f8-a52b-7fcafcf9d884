{% extends "base.html" %}

{% block title %}الأمان - Cyber Shield{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-shield-virus text-primary me-2"></i>
                        مركز الأمان
                    </h1>
                    <p class="text-muted mb-0">إدارة وحماية النظام من التهديدات السيبرانية</p>
                </div>
                <div>
                    <span class="badge bg-{{ 'success' if security.protection_enabled else 'danger' }} fs-6">
                        <i class="fas fa-{{ 'shield-alt' if security.protection_enabled else 'shield-virus' }} me-1"></i>
                        {{ 'الحماية نشطة' if security.protection_enabled else 'الحماية متوقفة' }}
                    </span>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Protection Control -->
    <div class="row mb-4">
        <div class="col-lg-8">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-transparent border-0">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-power-off text-primary me-2"></i>
                        التحكم في الحماية
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <div class="text-center">
                                <div class="security-status-icon mb-3">
                                    <i class="fas fa-shield-alt fa-4x text-{{ 'success' if security.protection_enabled else 'danger' }}"></i>
                                </div>
                                <h4 class="text-{{ 'success' if security.protection_enabled else 'danger' }}">
                                    {{ 'الحماية نشطة' if security.protection_enabled else 'الحماية متوقفة' }}
                                </h4>
                                <p class="text-muted">
                                    {{ 'النظام محمي من التهديدات' if security.protection_enabled else 'النظام غير محمي' }}
                                </p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="d-grid gap-2">
                                {% if security.protection_enabled %}
                                <button type="button" class="btn btn-danger btn-lg" id="stopProtection">
                                    <i class="fas fa-stop me-2"></i>
                                    إيقاف الحماية
                                </button>
                                {% else %}
                                <button type="button" class="btn btn-success btn-lg" id="startProtection">
                                    <i class="fas fa-play me-2"></i>
                                    بدء الحماية
                                </button>
                                {% endif %}
                                
                                <button type="button" class="btn btn-outline-primary" id="restartProtection">
                                    <i class="fas fa-redo me-2"></i>
                                    إعادة تشغيل الحماية
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-transparent border-0">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-cog text-primary me-2"></i>
                        مستوى الأمان
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label">اختر مستوى الحماية:</label>
                        <select class="form-select" id="securityLevel">
                            <option value="LOW" {{ 'selected' if security.security_level == 'LOW' else '' }}>منخفض</option>
                            <option value="MEDIUM" {{ 'selected' if security.security_level == 'MEDIUM' else '' }}>متوسط</option>
                            <option value="HIGH" {{ 'selected' if security.security_level == 'HIGH' else '' }}>عالي</option>
                            <option value="MAXIMUM" {{ 'selected' if security.security_level == 'MAXIMUM' else '' }}>أقصى حماية</option>
                        </select>
                    </div>
                    <button type="button" class="btn btn-primary w-100" id="updateSecurityLevel">
                        <i class="fas fa-save me-2"></i>
                        تطبيق التغييرات
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Scan Controls -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-transparent border-0">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-search text-primary me-2"></i>
                        فحص النظام
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="text-center p-3">
                                <i class="fas fa-search fa-2x text-info mb-2"></i>
                                <h6>فحص سريع</h6>
                                <p class="small text-muted">فحص الملفات الأساسية والذاكرة</p>
                                <button class="btn btn-info btn-sm w-100" id="quickScan">
                                    <i class="fas fa-play me-1"></i>بدء الفحص
                                </button>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center p-3">
                                <i class="fas fa-search-plus fa-2x text-warning mb-2"></i>
                                <h6>فحص شامل</h6>
                                <p class="small text-muted">فحص كامل لجميع الملفات</p>
                                <button class="btn btn-warning btn-sm w-100" id="fullScan">
                                    <i class="fas fa-play me-1"></i>بدء الفحص
                                </button>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center p-3">
                                <i class="fas fa-folder fa-2x text-secondary mb-2"></i>
                                <h6>فحص مخصص</h6>
                                <p class="small text-muted">فحص مجلد أو ملف محدد</p>
                                <button class="btn btn-secondary btn-sm w-100" id="customScan">
                                    <i class="fas fa-folder-open me-1"></i>اختيار مجلد
                                </button>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center p-3">
                                <i class="fas fa-download fa-2x text-success mb-2"></i>
                                <h6>تحديث التعريفات</h6>
                                <p class="small text-muted">تحديث قاعدة بيانات التهديدات</p>
                                <button class="btn btn-success btn-sm w-100" id="updateDefinitions">
                                    <i class="fas fa-download me-1"></i>تحديث
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Security Status -->
    <div class="row mb-4">
        <div class="col-lg-6">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-transparent border-0">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-pie text-primary me-2"></i>
                        حالة الحماية
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6 mb-3">
                            <div class="border-end">
                                <h4 class="text-success mb-0">{{ security.files_protected or 0 }}</h4>
                                <small class="text-muted">ملف محمي</small>
                            </div>
                        </div>
                        <div class="col-6 mb-3">
                            <h4 class="text-warning mb-0">{{ security.threats_blocked or 0 }}</h4>
                            <small class="text-muted">تهديد محظور</small>
                        </div>
                        <div class="col-6">
                            <div class="border-end">
                                <h4 class="text-info mb-0">{{ security.scans_completed or 0 }}</h4>
                                <small class="text-muted">فحص مكتمل</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <h4 class="text-primary mb-0">{{ security.quarantined_files or 0 }}</h4>
                            <small class="text-muted">ملف معزول</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-6">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-transparent border-0">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                        التهديدات الأخيرة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="threats-list" style="max-height: 200px; overflow-y: auto;">
                        {% if security.recent_threats %}
                            {% for threat in security.recent_threats[:5] %}
                            <div class="d-flex align-items-center mb-2 p-2 bg-light rounded">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-virus text-danger"></i>
                                </div>
                                <div class="flex-grow-1 ms-2">
                                    <div class="small fw-bold">{{ threat.threat_type }}</div>
                                    <div class="small text-muted">{{ threat.file_path }}</div>
                                </div>
                                <div class="flex-shrink-0">
                                    <span class="badge bg-{{ 'danger' if threat.severity == 'HIGH' else 'warning' }}">
                                        {{ threat.severity }}
                                    </span>
                                </div>
                            </div>
                            {% endfor %}
                        {% else %}
                            <div class="text-center text-muted py-3">
                                <i class="fas fa-shield-alt fa-2x mb-2"></i>
                                <p>لا توجد تهديدات مكتشفة</p>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Real-time Monitoring -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-transparent border-0">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-eye text-primary me-2"></i>
                            المراقبة المباشرة
                        </h5>
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="realTimeMonitoring" 
                                   {{ 'checked' if security.real_time_monitoring else '' }}>
                            <label class="form-check-label" for="realTimeMonitoring">
                                تفعيل المراقبة المباشرة
                            </label>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="monitoring-item">
                                <div class="d-flex align-items-center mb-2">
                                    <i class="fas fa-file text-info me-2"></i>
                                    <span class="fw-bold">مراقبة الملفات</span>
                                    <span class="badge bg-{{ 'success' if security.file_monitoring else 'secondary' }} ms-auto">
                                        {{ 'نشط' if security.file_monitoring else 'متوقف' }}
                                    </span>
                                </div>
                                <div class="progress mb-2" style="height: 5px;">
                                    <div class="progress-bar bg-info" style="width: {{ security.file_monitoring_load or 0 }}%"></div>
                                </div>
                                <small class="text-muted">{{ security.monitored_files or 0 }} ملف مراقب</small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="monitoring-item">
                                <div class="d-flex align-items-center mb-2">
                                    <i class="fas fa-network-wired text-warning me-2"></i>
                                    <span class="fw-bold">مراقبة الشبكة</span>
                                    <span class="badge bg-{{ 'success' if security.network_monitoring else 'secondary' }} ms-auto">
                                        {{ 'نشط' if security.network_monitoring else 'متوقف' }}
                                    </span>
                                </div>
                                <div class="progress mb-2" style="height: 5px;">
                                    <div class="progress-bar bg-warning" style="width: {{ security.network_monitoring_load or 0 }}%"></div>
                                </div>
                                <small class="text-muted">{{ security.network_connections or 0 }} اتصال مراقب</small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="monitoring-item">
                                <div class="d-flex align-items-center mb-2">
                                    <i class="fas fa-memory text-success me-2"></i>
                                    <span class="fw-bold">مراقبة الذاكرة</span>
                                    <span class="badge bg-{{ 'success' if security.memory_monitoring else 'secondary' }} ms-auto">
                                        {{ 'نشط' if security.memory_monitoring else 'متوقف' }}
                                    </span>
                                </div>
                                <div class="progress mb-2" style="height: 5px;">
                                    <div class="progress-bar bg-success" style="width: {{ security.memory_monitoring_load or 0 }}%"></div>
                                </div>
                                <small class="text-muted">{{ security.memory_usage or 0 }}% استخدام الذاكرة</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Scan Progress Modal -->
<div class="modal fade" id="scanModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-search me-2"></i>
                    <span id="scanTitle">جاري الفحص...</span>
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="progress mb-3" style="height: 25px;">
                    <div class="progress-bar progress-bar-striped progress-bar-animated bg-primary" 
                         id="scanProgress" style="width: 0%">
                        <span id="scanPercentage">0%</span>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="text-center">
                            <div id="scanStatus" class="h6">بدء الفحص...</div>
                            <div id="scanDetails" class="text-muted">جاري تحضير الفحص</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="scan-stats">
                            <div class="d-flex justify-content-between">
                                <span>الملفات المفحوصة:</span>
                                <span id="scannedFiles">0</span>
                            </div>
                            <div class="d-flex justify-content-between">
                                <span>التهديدات المكتشفة:</span>
                                <span id="threatsFound" class="text-danger">0</span>
                            </div>
                            <div class="d-flex justify-content-between">
                                <span>الوقت المنقضي:</span>
                                <span id="elapsedTime">00:00</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-danger" id="cancelScan">إلغاء الفحص</button>
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
    let scanStartTime = null;
    let scanTimer = null;
    
    // معالجات الأحداث
    document.getElementById('startProtection').addEventListener('click', async function() {
        await toggleProtection(true);
    });
    
    document.getElementById('stopProtection').addEventListener('click', async function() {
        if (confirm('هل أنت متأكد من إيقاف الحماية؟')) {
            await toggleProtection(false);
        }
    });
    
    document.getElementById('restartProtection').addEventListener('click', async function() {
        await restartProtection();
    });
    
    document.getElementById('updateSecurityLevel').addEventListener('click', async function() {
        await updateSecurityLevel();
    });
    
    document.getElementById('quickScan').addEventListener('click', function() {
        startScan('quick');
    });
    
    document.getElementById('fullScan').addEventListener('click', function() {
        startScan('full');
    });
    
    document.getElementById('updateDefinitions').addEventListener('click', async function() {
        await updateDefinitions();
    });
    
    // دوال الحماية
    async function toggleProtection(enable) {
        try {
            showLoading(true);
            const endpoint = enable ? '/api/security/start' : '/api/security/stop';
            const response = await fetch(endpoint, { method: 'POST' });
            const result = await response.json();
            
            if (result.success) {
                showNotification(result.message, 'success');
                setTimeout(() => location.reload(), 1000);
            } else {
                showNotification(result.message, 'error');
            }
        } catch (error) {
            showNotification('حدث خطأ في تغيير حالة الحماية', 'error');
        } finally {
            showLoading(false);
        }
    }
    
    async function restartProtection() {
        try {
            showLoading(true);
            showNotification('جاري إعادة تشغيل الحماية...', 'info');
            
            // إيقاف الحماية
            await fetch('/api/security/stop', { method: 'POST' });
            
            // انتظار ثانيتين
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            // بدء الحماية
            const response = await fetch('/api/security/start', { method: 'POST' });
            const result = await response.json();
            
            if (result.success) {
                showNotification('تم إعادة تشغيل الحماية بنجاح', 'success');
                setTimeout(() => location.reload(), 1000);
            } else {
                showNotification('فشل في إعادة تشغيل الحماية', 'error');
            }
        } catch (error) {
            showNotification('حدث خطأ في إعادة تشغيل الحماية', 'error');
        } finally {
            showLoading(false);
        }
    }
    
    async function updateSecurityLevel() {
        try {
            const level = document.getElementById('securityLevel').value;
            showLoading(true);
            
            const response = await fetch('/api/security/level', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ level: level })
            });
            
            const result = await response.json();
            
            if (result.success) {
                showNotification(`تم تعيين مستوى الأمان إلى ${level}`, 'success');
            } else {
                showNotification(result.message, 'error');
            }
        } catch (error) {
            showNotification('حدث خطأ في تحديث مستوى الأمان', 'error');
        } finally {
            showLoading(false);
        }
    }
    
    function startScan(type) {
        const modal = new bootstrap.Modal(document.getElementById('scanModal'));
        const title = type === 'quick' ? 'فحص سريع' : 'فحص شامل';
        
        document.getElementById('scanTitle').textContent = title;
        resetScanProgress();
        modal.show();
        
        // بدء الفحص
        fetch(`/api/scan/${type}`, { method: 'POST' })
            .then(response => response.json())
            .then(result => {
                if (result.success) {
                    scanStartTime = Date.now();
                    startScanTimer();
                } else {
                    showNotification(result.message, 'error');
                    modal.hide();
                }
            })
            .catch(error => {
                showNotification('حدث خطأ في بدء الفحص', 'error');
                modal.hide();
            });
    }
    
    function resetScanProgress() {
        document.getElementById('scanProgress').style.width = '0%';
        document.getElementById('scanPercentage').textContent = '0%';
        document.getElementById('scanStatus').textContent = 'بدء الفحص...';
        document.getElementById('scanDetails').textContent = 'جاري تحضير الفحص';
        document.getElementById('scannedFiles').textContent = '0';
        document.getElementById('threatsFound').textContent = '0';
        document.getElementById('elapsedTime').textContent = '00:00';
    }
    
    function startScanTimer() {
        scanTimer = setInterval(() => {
            if (scanStartTime) {
                const elapsed = Math.floor((Date.now() - scanStartTime) / 1000);
                const minutes = Math.floor(elapsed / 60);
                const seconds = elapsed % 60;
                document.getElementById('elapsedTime').textContent = 
                    `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            }
        }, 1000);
    }
    
    function updateScanProgress(progress, data = {}) {
        document.getElementById('scanProgress').style.width = progress + '%';
        document.getElementById('scanPercentage').textContent = progress + '%';
        
        if (data.scannedFiles) {
            document.getElementById('scannedFiles').textContent = data.scannedFiles;
        }
        
        if (data.threatsFound) {
            document.getElementById('threatsFound').textContent = data.threatsFound;
        }
        
        if (data.status) {
            document.getElementById('scanStatus').textContent = data.status;
        }
        
        if (data.details) {
            document.getElementById('scanDetails').textContent = data.details;
        }
        
        if (progress >= 100) {
            if (scanTimer) {
                clearInterval(scanTimer);
                scanTimer = null;
            }
            
            setTimeout(() => {
                bootstrap.Modal.getInstance(document.getElementById('scanModal')).hide();
                showNotification('تم الانتهاء من الفحص بنجاح', 'success');
            }, 2000);
        }
    }
    
    async function updateDefinitions() {
        try {
            showLoading(true);
            showNotification('جاري تحديث قاعدة بيانات التهديدات...', 'info');
            
            const response = await fetch('/api/security/update-definitions', { method: 'POST' });
            const result = await response.json();
            
            if (result.success) {
                showNotification('تم تحديث قاعدة بيانات التهديدات بنجاح', 'success');
            } else {
                showNotification(result.message, 'error');
            }
        } catch (error) {
            showNotification('حدث خطأ في تحديث قاعدة البيانات', 'error');
        } finally {
            showLoading(false);
        }
    }
    
    // استقبال تحديثات الفحص من Socket.IO
    if (typeof socket !== 'undefined') {
        socket.on('scan_progress', function(data) {
            updateScanProgress(data.progress, data);
        });
    }
</script>
{% endblock %}
