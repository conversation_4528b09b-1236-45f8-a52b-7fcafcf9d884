# -*- coding: utf-8 -*-
"""
Cyber Shield - Logs Manager
مدير السجلات والتقارير
"""

import logging
import json
import csv
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta
from pathlib import Path

from src.database.db_manager import DatabaseManager
from src.auth.permissions import Permission, require_permission
from config.settings import *

logger = logging.getLogger(__name__)

class LogsManager:
    """
    مدير السجلات والتقارير للوحة الإدارة
    """
    
    def __init__(self):
        self.db_manager = DatabaseManager()
    
    @require_permission(Permission.VIEW_ALL_LOGS)
    def get_activity_logs(self, user_data: Dict, filters: Dict = None, limit: int = 100) -> List[Dict]:
        """
        الحصول على سجلات الأنشطة مع الفلاتر
        """
        try:
            # بناء الاستعلام الأساسي
            query = """
                SELECT al.*, u.username, u.full_name 
                FROM activity_logs al
                LEFT JOIN users u ON al.user_id = u.id
            """
            
            conditions = []
            params = []
            
            # تطبيق الفلاتر
            if filters:
                if filters.get('user_id'):
                    conditions.append("al.user_id = ?")
                    params.append(filters['user_id'])
                
                if filters.get('action'):
                    conditions.append("al.action LIKE ?")
                    params.append(f"%{filters['action']}%")
                
                if filters.get('severity'):
                    conditions.append("al.severity = ?")
                    params.append(filters['severity'])
                
                if filters.get('start_date'):
                    conditions.append("al.created_at >= ?")
                    params.append(filters['start_date'])
                
                if filters.get('end_date'):
                    conditions.append("al.created_at <= ?")
                    params.append(filters['end_date'])
                
                if filters.get('ip_address'):
                    conditions.append("al.ip_address = ?")
                    params.append(filters['ip_address'])
            
            # إضافة الشروط للاستعلام
            if conditions:
                query += " WHERE " + " AND ".join(conditions)
            
            query += " ORDER BY al.created_at DESC LIMIT ?"
            params.append(limit)
            
            logs = self.db_manager.execute_query(query, tuple(params))
            
            # معالجة البيانات الإضافية
            for log in logs:
                if log.get('additional_data'):
                    try:
                        log['additional_data'] = json.loads(log['additional_data'])
                    except:
                        pass
            
            return logs
            
        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على سجلات الأنشطة: {e}")
            return []
    
    @require_permission(Permission.VIEW_ALL_LOGS)
    def get_security_logs(self, user_data: Dict, filters: Dict = None, limit: int = 100) -> List[Dict]:
        """
        الحصول على السجلات الأمنية
        """
        try:
            # سجلات الأمان (تحذيرات وأخطاء)
            security_filters = filters.copy() if filters else {}
            security_filters['severity'] = ['WARNING', 'ERROR', 'CRITICAL']
            
            query = """
                SELECT al.*, u.username, u.full_name 
                FROM activity_logs al
                LEFT JOIN users u ON al.user_id = u.id
                WHERE al.severity IN ('WARNING', 'ERROR', 'CRITICAL')
            """
            
            conditions = []
            params = []
            
            # تطبيق الفلاتر الإضافية
            if filters:
                if filters.get('user_id'):
                    conditions.append("al.user_id = ?")
                    params.append(filters['user_id'])
                
                if filters.get('action'):
                    conditions.append("al.action LIKE ?")
                    params.append(f"%{filters['action']}%")
                
                if filters.get('start_date'):
                    conditions.append("al.created_at >= ?")
                    params.append(filters['start_date'])
                
                if filters.get('end_date'):
                    conditions.append("al.created_at <= ?")
                    params.append(filters['end_date'])
            
            if conditions:
                query += " AND " + " AND ".join(conditions)
            
            query += " ORDER BY al.created_at DESC LIMIT ?"
            params.append(limit)
            
            return self.db_manager.execute_query(query, tuple(params))
            
        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على السجلات الأمنية: {e}")
            return []
    
    @require_permission(Permission.VIEW_ALL_LOGS)
    def get_login_logs(self, user_data: Dict, filters: Dict = None, limit: int = 100) -> List[Dict]:
        """
        الحصول على سجلات تسجيل الدخول
        """
        try:
            query = """
                SELECT al.*, u.username, u.full_name 
                FROM activity_logs al
                LEFT JOIN users u ON al.user_id = u.id
                WHERE al.action IN ('USER_LOGIN', 'LOGIN_FAILED', 'USER_LOGOUT')
            """
            
            conditions = []
            params = []
            
            # تطبيق الفلاتر
            if filters:
                if filters.get('user_id'):
                    conditions.append("al.user_id = ?")
                    params.append(filters['user_id'])
                
                if filters.get('success_only'):
                    conditions.append("al.action = 'USER_LOGIN'")
                
                if filters.get('failed_only'):
                    conditions.append("al.action = 'LOGIN_FAILED'")
                
                if filters.get('start_date'):
                    conditions.append("al.created_at >= ?")
                    params.append(filters['start_date'])
                
                if filters.get('end_date'):
                    conditions.append("al.created_at <= ?")
                    params.append(filters['end_date'])
            
            if conditions:
                query += " AND " + " AND ".join(conditions)
            
            query += " ORDER BY al.created_at DESC LIMIT ?"
            params.append(limit)
            
            return self.db_manager.execute_query(query, tuple(params))
            
        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على سجلات تسجيل الدخول: {e}")
            return []
    
    @require_permission(Permission.VIEW_ALL_LOGS)
    def get_system_statistics(self, user_data: Dict) -> Dict:
        """
        الحصول على إحصائيات النظام
        """
        try:
            stats = {}
            
            # إحصائيات المستخدمين
            stats['users'] = self._get_users_statistics()
            
            # إحصائيات الأنشطة
            stats['activities'] = self._get_activities_statistics()
            
            # إحصائيات الأمان
            stats['security'] = self._get_security_statistics()
            
            # إحصائيات الشبكة
            stats['network'] = self._get_network_statistics()
            
            # إحصائيات الملفات
            stats['files'] = self._get_files_statistics()
            
            return stats
            
        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على إحصائيات النظام: {e}")
            return {}
    
    @require_permission(Permission.VIEW_ALL_LOGS)
    def generate_report(self, user_data: Dict, report_type: str, filters: Dict = None) -> str:
        """
        إنشاء تقرير
        """
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            if report_type == 'activity':
                return self._generate_activity_report(filters, timestamp)
            elif report_type == 'security':
                return self._generate_security_report(filters, timestamp)
            elif report_type == 'users':
                return self._generate_users_report(filters, timestamp)
            elif report_type == 'system':
                return self._generate_system_report(filters, timestamp)
            else:
                logger.error(f"❌ نوع تقرير غير مدعوم: {report_type}")
                return None
                
        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء التقرير: {e}")
            return None
    
    @require_permission(Permission.VIEW_ALL_LOGS)
    def export_logs(self, user_data: Dict, log_type: str, format_type: str = 'csv', filters: Dict = None) -> str:
        """
        تصدير السجلات
        """
        try:
            # الحصول على البيانات
            if log_type == 'activity':
                data = self.get_activity_logs(user_data, filters, limit=10000)
            elif log_type == 'security':
                data = self.get_security_logs(user_data, filters, limit=10000)
            elif log_type == 'login':
                data = self.get_login_logs(user_data, filters, limit=10000)
            else:
                return None
            
            if not data:
                return None
            
            # إنشاء اسم الملف
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{log_type}_logs_{timestamp}.{format_type}"
            export_file = TEMP_DIR / filename
            
            # تصدير البيانات
            if format_type == 'csv':
                return self._export_to_csv(data, export_file)
            elif format_type == 'json':
                return self._export_to_json(data, export_file)
            else:
                return None
                
        except Exception as e:
            logger.error(f"❌ خطأ في تصدير السجلات: {e}")
            return None
    
    def _get_users_statistics(self) -> Dict:
        """
        إحصائيات المستخدمين
        """
        try:
            stats = {}
            
            # إجمالي المستخدمين
            query = "SELECT COUNT(*) as count FROM users"
            result = self.db_manager.execute_query(query)
            stats['total_users'] = result[0]['count'] if result else 0
            
            # المستخدمين النشطين
            query = "SELECT COUNT(*) as count FROM users WHERE is_active = TRUE"
            result = self.db_manager.execute_query(query)
            stats['active_users'] = result[0]['count'] if result else 0
            
            # المديرين
            query = "SELECT COUNT(*) as count FROM users WHERE is_admin = TRUE"
            result = self.db_manager.execute_query(query)
            stats['admin_users'] = result[0]['count'] if result else 0
            
            # المستخدمين المقفلين
            query = "SELECT COUNT(*) as count FROM users WHERE locked_until > CURRENT_TIMESTAMP"
            result = self.db_manager.execute_query(query)
            stats['locked_users'] = result[0]['count'] if result else 0
            
            # المستخدمين الجدد (آخر 30 يوم)
            query = """
                SELECT COUNT(*) as count FROM users 
                WHERE created_at >= datetime('now', '-30 days')
            """
            result = self.db_manager.execute_query(query)
            stats['new_users_30d'] = result[0]['count'] if result else 0
            
            return stats
            
        except Exception as e:
            logger.error(f"❌ خطأ في إحصائيات المستخدمين: {e}")
            return {}
    
    def _get_activities_statistics(self) -> Dict:
        """
        إحصائيات الأنشطة
        """
        try:
            stats = {}
            
            # إجمالي الأنشطة
            query = "SELECT COUNT(*) as count FROM activity_logs"
            result = self.db_manager.execute_query(query)
            stats['total_activities'] = result[0]['count'] if result else 0
            
            # الأنشطة اليوم
            query = """
                SELECT COUNT(*) as count FROM activity_logs 
                WHERE DATE(created_at) = DATE('now')
            """
            result = self.db_manager.execute_query(query)
            stats['activities_today'] = result[0]['count'] if result else 0
            
            # تسجيلات الدخول اليوم
            query = """
                SELECT COUNT(*) as count FROM activity_logs 
                WHERE action = 'USER_LOGIN' AND DATE(created_at) = DATE('now')
            """
            result = self.db_manager.execute_query(query)
            stats['logins_today'] = result[0]['count'] if result else 0
            
            # محاولات الدخول الفاشلة اليوم
            query = """
                SELECT COUNT(*) as count FROM activity_logs 
                WHERE action = 'LOGIN_FAILED' AND DATE(created_at) = DATE('now')
            """
            result = self.db_manager.execute_query(query)
            stats['failed_logins_today'] = result[0]['count'] if result else 0
            
            return stats
            
        except Exception as e:
            logger.error(f"❌ خطأ في إحصائيات الأنشطة: {e}")
            return {}
    
    def _get_security_statistics(self) -> Dict:
        """
        إحصائيات الأمان
        """
        try:
            stats = {}
            
            # التهديدات المكتشفة
            query = "SELECT COUNT(*) as count FROM detected_threats"
            result = self.db_manager.execute_query(query)
            stats['total_threats'] = result[0]['count'] if result else 0
            
            # التهديدات اليوم
            query = """
                SELECT COUNT(*) as count FROM detected_threats 
                WHERE DATE(detected_at) = DATE('now')
            """
            result = self.db_manager.execute_query(query)
            stats['threats_today'] = result[0]['count'] if result else 0
            
            # التهديدات عالية الخطورة
            query = """
                SELECT COUNT(*) as count FROM detected_threats 
                WHERE severity IN ('HIGH', 'CRITICAL')
            """
            result = self.db_manager.execute_query(query)
            stats['high_severity_threats'] = result[0]['count'] if result else 0
            
            # الأحداث الأمنية
            query = """
                SELECT COUNT(*) as count FROM activity_logs 
                WHERE severity IN ('WARNING', 'ERROR', 'CRITICAL')
            """
            result = self.db_manager.execute_query(query)
            stats['security_events'] = result[0]['count'] if result else 0
            
            return stats
            
        except Exception as e:
            logger.error(f"❌ خطأ في إحصائيات الأمان: {e}")
            return {}
    
    def _get_network_statistics(self) -> Dict:
        """
        إحصائيات الشبكة
        """
        try:
            stats = {}
            
            # إجمالي الاتصالات المراقبة
            query = "SELECT COUNT(*) as count FROM network_monitoring"
            result = self.db_manager.execute_query(query)
            stats['total_connections'] = result[0]['count'] if result else 0
            
            # الاتصالات المشبوهة
            query = "SELECT COUNT(*) as count FROM network_monitoring WHERE is_suspicious = TRUE"
            result = self.db_manager.execute_query(query)
            stats['suspicious_connections'] = result[0]['count'] if result else 0
            
            # عناوين IP الفريدة
            query = "SELECT COUNT(DISTINCT remote_address) as count FROM network_monitoring"
            result = self.db_manager.execute_query(query)
            stats['unique_ips'] = result[0]['count'] if result else 0
            
            return stats
            
        except Exception as e:
            logger.error(f"❌ خطأ في إحصائيات الشبكة: {e}")
            return {}
    
    def _get_files_statistics(self) -> Dict:
        """
        إحصائيات الملفات
        """
        try:
            stats = {}
            
            # إجمالي أحداث الملفات
            query = "SELECT COUNT(*) as count FROM file_monitoring"
            result = self.db_manager.execute_query(query)
            stats['total_file_events'] = result[0]['count'] if result else 0
            
            # الملفات المشبوهة
            query = "SELECT COUNT(*) as count FROM file_monitoring WHERE is_suspicious = TRUE"
            result = self.db_manager.execute_query(query)
            stats['suspicious_files'] = result[0]['count'] if result else 0
            
            # أحداث الملفات اليوم
            query = """
                SELECT COUNT(*) as count FROM file_monitoring 
                WHERE DATE(detected_at) = DATE('now')
            """
            result = self.db_manager.execute_query(query)
            stats['file_events_today'] = result[0]['count'] if result else 0
            
            return stats
            
        except Exception as e:
            logger.error(f"❌ خطأ في إحصائيات الملفات: {e}")
            return {}
    
    def _export_to_csv(self, data: List[Dict], file_path: Path) -> str:
        """
        تصدير البيانات إلى CSV
        """
        try:
            if not data:
                return None
            
            with open(file_path, 'w', newline='', encoding='utf-8') as f:
                writer = csv.DictWriter(f, fieldnames=data[0].keys())
                writer.writeheader()
                writer.writerows(data)
            
            return str(file_path)
            
        except Exception as e:
            logger.error(f"❌ خطأ في تصدير CSV: {e}")
            return None
    
    def _export_to_json(self, data: List[Dict], file_path: Path) -> str:
        """
        تصدير البيانات إلى JSON
        """
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2, default=str)
            
            return str(file_path)
            
        except Exception as e:
            logger.error(f"❌ خطأ في تصدير JSON: {e}")
            return None
