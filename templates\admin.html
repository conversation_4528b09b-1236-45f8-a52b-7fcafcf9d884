{% extends "base.html" %}

{% block title %}لوحة الإدارة - Cyber Shield{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-cogs text-primary me-2"></i>
                        لوحة الإدارة
                    </h1>
                    <p class="text-muted mb-0">إدارة شاملة للنظام والمستخدمين</p>
                </div>
                <div>
                    <span class="badge bg-success fs-6">
                        <i class="fas fa-crown me-1"></i>
                        مدير النظام
                    </span>
                </div>
            </div>
        </div>
    </div>

    <!-- Admin Navigation -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <nav>
                        <div class="nav nav-pills justify-content-center" id="nav-tab" role="tablist">
                            <button class="nav-link active" id="nav-users-tab" data-bs-toggle="tab" data-bs-target="#nav-users" type="button" role="tab">
                                <i class="fas fa-users me-1"></i>إدارة المستخدمين
                            </button>
                            <button class="nav-link" id="nav-logs-tab" data-bs-toggle="tab" data-bs-target="#nav-logs" type="button" role="tab">
                                <i class="fas fa-list-alt me-1"></i>السجلات
                            </button>
                            <button class="nav-link" id="nav-settings-tab" data-bs-toggle="tab" data-bs-target="#nav-settings" type="button" role="tab">
                                <i class="fas fa-cog me-1"></i>الإعدادات
                            </button>
                            <button class="nav-link" id="nav-backup-tab" data-bs-toggle="tab" data-bs-target="#nav-backup" type="button" role="tab">
                                <i class="fas fa-database me-1"></i>النسخ الاحتياطية
                            </button>
                            <button class="nav-link" id="nav-system-tab" data-bs-toggle="tab" data-bs-target="#nav-system" type="button" role="tab">
                                <i class="fas fa-server me-1"></i>معلومات النظام
                            </button>
                        </div>
                    </nav>
                </div>
            </div>
        </div>
    </div>

    <!-- Tab Content -->
    <div class="tab-content" id="nav-tabContent">
        <!-- Users Management -->
        <div class="tab-pane fade show active" id="nav-users" role="tabpanel">
            <div class="row">
                <div class="col-12">
                    <div class="card border-0 shadow-sm">
                        <div class="card-header bg-transparent border-0">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-users text-primary me-2"></i>
                                    إدارة المستخدمين
                                </h5>
                                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addUserModal">
                                    <i class="fas fa-plus me-1"></i>إضافة مستخدم
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover" id="usersTable">
                                    <thead>
                                        <tr>
                                            <th>اسم المستخدم</th>
                                            <th>البريد الإلكتروني</th>
                                            <th>الاسم الكامل</th>
                                            <th>الصلاحيات</th>
                                            <th>الحالة</th>
                                            <th>آخر دخول</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <!-- سيتم ملؤها بـ JavaScript -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Logs Management -->
        <div class="tab-pane fade" id="nav-logs" role="tabpanel">
            <div class="row mb-3">
                <div class="col-md-3">
                    <select class="form-select" id="logType">
                        <option value="activity">سجلات الأنشطة</option>
                        <option value="security">السجلات الأمنية</option>
                        <option value="login">سجلات تسجيل الدخول</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <select class="form-select" id="logSeverity">
                        <option value="">جميع المستويات</option>
                        <option value="INFO">معلومات</option>
                        <option value="WARNING">تحذير</option>
                        <option value="ERROR">خطأ</option>
                        <option value="CRITICAL">حرج</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <input type="date" class="form-control" id="logDate">
                </div>
                <div class="col-md-3">
                    <button class="btn btn-primary w-100" id="loadLogs">
                        <i class="fas fa-search me-1"></i>تحميل السجلات
                    </button>
                </div>
            </div>

            <div class="card border-0 shadow-sm">
                <div class="card-header bg-transparent border-0">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-list-alt text-primary me-2"></i>
                            السجلات
                        </h5>
                        <button class="btn btn-outline-primary" id="exportLogs">
                            <i class="fas fa-download me-1"></i>تصدير
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm" id="logsTable">
                            <thead>
                                <tr>
                                    <th>الوقت</th>
                                    <th>المستخدم</th>
                                    <th>النشاط</th>
                                    <th>الوصف</th>
                                    <th>المستوى</th>
                                    <th>عنوان IP</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- سيتم ملؤها بـ JavaScript -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Settings -->
        <div class="tab-pane fade" id="nav-settings" role="tabpanel">
            <div class="row">
                <div class="col-lg-6">
                    <div class="card border-0 shadow-sm">
                        <div class="card-header bg-transparent border-0">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-shield-alt text-primary me-2"></i>
                                إعدادات الأمان
                            </h5>
                        </div>
                        <div class="card-body">
                            <form id="securitySettingsForm">
                                <div class="mb-3">
                                    <label class="form-label">مدة انتهاء الجلسة (دقيقة)</label>
                                    <input type="number" class="form-control" name="session_timeout" value="60" min="5" max="1440">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">عدد محاولات تسجيل الدخول المسموحة</label>
                                    <input type="number" class="form-control" name="max_login_attempts" value="5" min="3" max="10">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">مدة حظر المستخدم (دقيقة)</label>
                                    <input type="number" class="form-control" name="lockout_duration" value="30" min="5" max="1440">
                                </div>
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="require_2fa" id="require2fa">
                                        <label class="form-check-label" for="require2fa">
                                            إجبار التحقق بخطوتين لجميع المستخدمين
                                        </label>
                                    </div>
                                </div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-1"></i>حفظ الإعدادات
                                </button>
                            </form>
                        </div>
                    </div>
                </div>

                <div class="col-lg-6">
                    <div class="card border-0 shadow-sm">
                        <div class="card-header bg-transparent border-0">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-envelope text-primary me-2"></i>
                                إعدادات البريد الإلكتروني
                            </h5>
                        </div>
                        <div class="card-body">
                            <form id="emailSettingsForm">
                                <div class="mb-3">
                                    <label class="form-label">خادم SMTP</label>
                                    <input type="text" class="form-control" name="smtp_server" placeholder="smtp.gmail.com">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">منفذ SMTP</label>
                                    <input type="number" class="form-control" name="smtp_port" value="587">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">البريد الإلكتروني للمرسل</label>
                                    <input type="email" class="form-control" name="email_sender">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">كلمة مرور التطبيق</label>
                                    <input type="password" class="form-control" name="email_password">
                                </div>
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="email_notifications" id="emailNotifications">
                                        <label class="form-check-label" for="emailNotifications">
                                            تفعيل إشعارات البريد الإلكتروني
                                        </label>
                                    </div>
                                </div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-1"></i>حفظ الإعدادات
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Backup -->
        <div class="tab-pane fade" id="nav-backup" role="tabpanel">
            <div class="row">
                <div class="col-lg-6">
                    <div class="card border-0 shadow-sm">
                        <div class="card-header bg-transparent border-0">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-download text-success me-2"></i>
                                إنشاء نسخة احتياطية
                            </h5>
                        </div>
                        <div class="card-body">
                            <p class="text-muted">إنشاء نسخة احتياطية من قاعدة البيانات والإعدادات</p>
                            <div class="d-grid">
                                <button class="btn btn-success btn-lg" id="createBackup">
                                    <i class="fas fa-download me-2"></i>
                                    إنشاء نسخة احتياطية
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-6">
                    <div class="card border-0 shadow-sm">
                        <div class="card-header bg-transparent border-0">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-upload text-warning me-2"></i>
                                استعادة نسخة احتياطية
                            </h5>
                        </div>
                        <div class="card-body">
                            <p class="text-muted">استعادة النظام من نسخة احتياطية سابقة</p>
                            <div class="mb-3">
                                <input type="file" class="form-control" id="backupFile" accept=".zip">
                            </div>
                            <div class="d-grid">
                                <button class="btn btn-warning btn-lg" id="restoreBackup">
                                    <i class="fas fa-upload me-2"></i>
                                    استعادة النسخة الاحتياطية
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-12">
                    <div class="card border-0 shadow-sm">
                        <div class="card-header bg-transparent border-0">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-history text-info me-2"></i>
                                النسخ الاحتياطية السابقة
                            </h5>
                        </div>
                        <div class="card-body">
                            <div id="backupHistory">
                                <!-- سيتم ملؤها بـ JavaScript -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- System Info -->
        <div class="tab-pane fade" id="nav-system" role="tabpanel">
            <div class="row">
                <div class="col-lg-6">
                    <div class="card border-0 shadow-sm">
                        <div class="card-header bg-transparent border-0">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-info-circle text-primary me-2"></i>
                                معلومات النظام
                            </h5>
                        </div>
                        <div class="card-body">
                            <div id="systemInfo">
                                <!-- سيتم ملؤها بـ JavaScript -->
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-6">
                    <div class="card border-0 shadow-sm">
                        <div class="card-header bg-transparent border-0">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-chart-line text-success me-2"></i>
                                أداء النظام
                            </h5>
                        </div>
                        <div class="card-body">
                            <canvas id="systemPerformanceChart" height="200"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-12">
                    <div class="card border-0 shadow-sm">
                        <div class="card-header bg-transparent border-0">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-network-wired text-warning me-2"></i>
                                معلومات الشبكة
                            </h5>
                        </div>
                        <div class="card-body">
                            <div id="networkInfo">
                                <!-- سيتم ملؤها بـ JavaScript -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add User Modal -->
<div class="modal fade" id="addUserModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-user-plus me-2"></i>
                    إضافة مستخدم جديد
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addUserForm">
                    <div class="mb-3">
                        <label class="form-label">اسم المستخدم</label>
                        <input type="text" class="form-control" name="username" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">البريد الإلكتروني</label>
                        <input type="email" class="form-control" name="email" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">الاسم الكامل</label>
                        <input type="text" class="form-control" name="full_name" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">كلمة المرور</label>
                        <input type="password" class="form-control" name="password" required>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="is_admin" id="isAdmin">
                            <label class="form-check-label" for="isAdmin">
                                صلاحيات المدير
                            </label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" id="saveUser">
                    <i class="fas fa-save me-1"></i>حفظ المستخدم
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
    // تهيئة الصفحة
    document.addEventListener('DOMContentLoaded', function() {
        loadUsers();
        loadSystemInfo();
        loadNetworkInfo();
        initializeSystemChart();

        // معالجات الأحداث
        document.getElementById('loadLogs').addEventListener('click', loadLogs);
        document.getElementById('saveUser').addEventListener('click', saveUser);
        document.getElementById('createBackup').addEventListener('click', createBackup);
        document.getElementById('restoreBackup').addEventListener('click', restoreBackup);
        document.getElementById('securitySettingsForm').addEventListener('submit', saveSecuritySettings);
        document.getElementById('emailSettingsForm').addEventListener('submit', saveEmailSettings);
    });

    // إدارة المستخدمين
    async function loadUsers() {
        try {
            const response = await fetch('/api/admin/users');
            const result = await response.json();

            if (result.success) {
                displayUsers(result.data);
            } else {
                showNotification('فشل في تحميل المستخدمين', 'error');
            }
        } catch (error) {
            showNotification('حدث خطأ في تحميل المستخدمين', 'error');
        }
    }

    function displayUsers(users) {
        const tbody = document.querySelector('#usersTable tbody');

        if (!users || users.length === 0) {
            tbody.innerHTML = '<tr><td colspan="7" class="text-center text-muted">لا يوجد مستخدمون</td></tr>';
            return;
        }

        const html = users.map(user => `
            <tr>
                <td>${user.username}</td>
                <td>${user.email}</td>
                <td>${user.full_name || '-'}</td>
                <td>
                    ${user.is_admin ? '<span class="badge bg-danger">مدير</span>' : '<span class="badge bg-secondary">مستخدم</span>'}
                </td>
                <td>
                    ${user.is_active ? '<span class="badge bg-success">نشط</span>' : '<span class="badge bg-danger">معطل</span>'}
                </td>
                <td>${user.last_login ? formatDate(user.last_login) : 'لم يسجل دخول'}</td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="editUser(${user.id})">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-outline-warning" onclick="toggleUserStatus(${user.id}, ${!user.is_active})">
                            <i class="fas fa-${user.is_active ? 'ban' : 'check'}"></i>
                        </button>
                        ${!user.is_admin ? `<button class="btn btn-outline-danger" onclick="deleteUser(${user.id})"><i class="fas fa-trash"></i></button>` : ''}
                    </div>
                </td>
            </tr>
        `).join('');

        tbody.innerHTML = html;
    }

    async function saveUser() {
        try {
            const form = document.getElementById('addUserForm');
            const formData = new FormData(form);

            const userData = {
                username: formData.get('username'),
                email: formData.get('email'),
                full_name: formData.get('full_name'),
                password: formData.get('password'),
                is_admin: formData.get('is_admin') === 'on'
            };

            const response = await fetch('/api/admin/users', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(userData)
            });

            const result = await response.json();

            if (result.success) {
                showNotification('تم إضافة المستخدم بنجاح', 'success');
                bootstrap.Modal.getInstance(document.getElementById('addUserModal')).hide();
                form.reset();
                loadUsers();
            } else {
                showNotification(result.message, 'error');
            }
        } catch (error) {
            showNotification('حدث خطأ في إضافة المستخدم', 'error');
        }
    }

    async function toggleUserStatus(userId, activate) {
        try {
            const response = await fetch(`/api/admin/users/${userId}/toggle`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ activate: activate })
            });

            const result = await response.json();

            if (result.success) {
                showNotification(result.message, 'success');
                loadUsers();
            } else {
                showNotification(result.message, 'error');
            }
        } catch (error) {
            showNotification('حدث خطأ في تغيير حالة المستخدم', 'error');
        }
    }

    async function deleteUser(userId) {
        if (!confirm('هل أنت متأكد من حذف هذا المستخدم؟')) {
            return;
        }

        try {
            const response = await fetch(`/api/admin/users/${userId}`, {
                method: 'DELETE'
            });

            const result = await response.json();

            if (result.success) {
                showNotification('تم حذف المستخدم بنجاح', 'success');
                loadUsers();
            } else {
                showNotification(result.message, 'error');
            }
        } catch (error) {
            showNotification('حدث خطأ في حذف المستخدم', 'error');
        }
    }

    // إدارة السجلات
    async function loadLogs() {
        try {
            const logType = document.getElementById('logType').value;
            const severity = document.getElementById('logSeverity').value;
            const date = document.getElementById('logDate').value;

            let url = `/api/logs/${logType}?limit=100`;
            if (severity) url += `&severity=${severity}`;
            if (date) url += `&date=${date}`;

            const response = await fetch(url);
            const result = await response.json();

            if (result.success) {
                displayLogs(result.data);
            } else {
                showNotification('فشل في تحميل السجلات', 'error');
            }
        } catch (error) {
            showNotification('حدث خطأ في تحميل السجلات', 'error');
        }
    }

    function displayLogs(logs) {
        const tbody = document.querySelector('#logsTable tbody');

        if (!logs || logs.length === 0) {
            tbody.innerHTML = '<tr><td colspan="6" class="text-center text-muted">لا توجد سجلات</td></tr>';
            return;
        }

        const html = logs.map(log => `
            <tr>
                <td>${formatDate(log.created_at)}</td>
                <td>${log.username || 'النظام'}</td>
                <td>${log.action}</td>
                <td>${log.description}</td>
                <td>
                    <span class="badge bg-${getSeverityColor(log.severity)}">${log.severity}</span>
                </td>
                <td>${log.ip_address || '-'}</td>
            </tr>
        `).join('');

        tbody.innerHTML = html;
    }

    function getSeverityColor(severity) {
        const colors = {
            'INFO': 'info',
            'WARNING': 'warning',
            'ERROR': 'danger',
            'CRITICAL': 'dark'
        };
        return colors[severity] || 'secondary';
    }

    // النسخ الاحتياطية
    async function createBackup() {
        try {
            showLoading(true);
            showNotification('جاري إنشاء النسخة الاحتياطية...', 'info');

            const response = await fetch('/api/admin/backup', { method: 'POST' });
            const result = await response.json();

            if (result.success) {
                showNotification('تم إنشاء النسخة الاحتياطية بنجاح', 'success');

                // تحميل الملف
                const link = document.createElement('a');
                link.href = `/api/admin/backup/download/${result.filename}`;
                link.download = result.filename;
                link.click();
            } else {
                showNotification(result.message, 'error');
            }
        } catch (error) {
            showNotification('حدث خطأ في إنشاء النسخة الاحتياطية', 'error');
        } finally {
            showLoading(false);
        }
    }

    async function restoreBackup() {
        const fileInput = document.getElementById('backupFile');
        const file = fileInput.files[0];

        if (!file) {
            showNotification('يرجى اختيار ملف النسخة الاحتياطية', 'warning');
            return;
        }

        if (!confirm('هل أنت متأكد من استعادة النسخة الاحتياطية؟ سيتم استبدال البيانات الحالية.')) {
            return;
        }

        try {
            showLoading(true);
            showNotification('جاري استعادة النسخة الاحتياطية...', 'info');

            const formData = new FormData();
            formData.append('backup_file', file);

            const response = await fetch('/api/admin/backup/restore', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (result.success) {
                showNotification('تم استعادة النسخة الاحتياطية بنجاح', 'success');
                setTimeout(() => location.reload(), 2000);
            } else {
                showNotification(result.message, 'error');
            }
        } catch (error) {
            showNotification('حدث خطأ في استعادة النسخة الاحتياطية', 'error');
        } finally {
            showLoading(false);
        }
    }

    // معلومات النظام
    async function loadSystemInfo() {
        try {
            const response = await fetch('/api/admin/system-info');
            const result = await response.json();

            if (result.success) {
                displaySystemInfo(result.data);
            }
        } catch (error) {
            console.error('خطأ في تحميل معلومات النظام:', error);
        }
    }

    function displaySystemInfo(info) {
        const container = document.getElementById('systemInfo');

        const html = `
            <table class="table table-sm">
                <tr><td><strong>نظام التشغيل:</strong></td><td>${info.platform || '-'}</td></tr>
                <tr><td><strong>إصدار النظام:</strong></td><td>${info.platform_release || '-'}</td></tr>
                <tr><td><strong>المعمارية:</strong></td><td>${info.architecture || '-'}</td></tr>
                <tr><td><strong>اسم الجهاز:</strong></td><td>${info.hostname || '-'}</td></tr>
                <tr><td><strong>المعالج:</strong></td><td>${info.processor || '-'}</td></tr>
                <tr><td><strong>الذاكرة:</strong></td><td>${info.ram || '-'}</td></tr>
                <tr><td><strong>عدد المعالجات:</strong></td><td>${info.cpu_count || '-'}</td></tr>
                <tr><td><strong>وقت التشغيل:</strong></td><td>${info.boot_time || '-'}</td></tr>
            </table>
        `;

        container.innerHTML = html;
    }

    async function loadNetworkInfo() {
        try {
            const response = await fetch('/api/admin/network-info');
            const result = await response.json();

            if (result.success) {
                displayNetworkInfo(result.data);
            }
        } catch (error) {
            console.error('خطأ في تحميل معلومات الشبكة:', error);
        }
    }

    function displayNetworkInfo(info) {
        const container = document.getElementById('networkInfo');

        let html = `
            <div class="row">
                <div class="col-md-6">
                    <h6>عنوان IP العام:</h6>
                    <p class="text-primary">${info.public_ip || 'غير متاح'}</p>

                    <h6>واجهات الشبكة:</h6>
                    <div class="small">
        `;

        if (info.interfaces) {
            Object.entries(info.interfaces).forEach(([name, addrs]) => {
                html += `<div class="mb-2"><strong>${name}:</strong><br>`;
                addrs.forEach(addr => {
                    html += `<span class="text-muted">${addr.ip}</span><br>`;
                });
                html += '</div>';
            });
        }

        html += `
                    </div>
                </div>
                <div class="col-md-6">
                    <h6>حالة الأمان:</h6>
                    <div class="small">
                        <div class="d-flex justify-content-between">
                            <span>جدار الحماية:</span>
                            <span class="badge bg-${info.security?.firewall_enabled ? 'success' : 'danger'}">
                                ${info.security?.firewall_enabled ? 'نشط' : 'معطل'}
                            </span>
                        </div>
                        <div class="d-flex justify-content-between">
                            <span>مكافح الفيروسات:</span>
                            <span class="badge bg-${info.security?.antivirus_running ? 'success' : 'danger'}">
                                ${info.security?.antivirus_running ? 'نشط' : 'معطل'}
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        `;

        container.innerHTML = html;
    }

    function initializeSystemChart() {
        const ctx = document.getElementById('systemPerformanceChart').getContext('2d');

        new Chart(ctx, {
            type: 'line',
            data: {
                labels: ['الآن', '-5 دقائق', '-10 دقائق', '-15 دقائق', '-20 دقائق'],
                datasets: [{
                    label: 'استخدام المعالج (%)',
                    data: [45, 52, 38, 41, 35],
                    borderColor: '#007bff',
                    backgroundColor: 'rgba(0, 123, 255, 0.1)',
                    tension: 0.4
                }, {
                    label: 'استخدام الذاكرة (%)',
                    data: [65, 68, 62, 59, 61],
                    borderColor: '#28a745',
                    backgroundColor: 'rgba(40, 167, 69, 0.1)',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100
                    }
                }
            }
        });
    }

    // حفظ الإعدادات
    async function saveSecuritySettings(e) {
        e.preventDefault();

        try {
            const formData = new FormData(e.target);
            const settings = Object.fromEntries(formData);

            const response = await fetch('/api/admin/settings/security', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(settings)
            });

            const result = await response.json();

            if (result.success) {
                showNotification('تم حفظ إعدادات الأمان بنجاح', 'success');
            } else {
                showNotification(result.message, 'error');
            }
        } catch (error) {
            showNotification('حدث خطأ في حفظ الإعدادات', 'error');
        }
    }

    async function saveEmailSettings(e) {
        e.preventDefault();

        try {
            const formData = new FormData(e.target);
            const settings = Object.fromEntries(formData);

            const response = await fetch('/api/admin/settings/email', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(settings)
            });

            const result = await response.json();

            if (result.success) {
                showNotification('تم حفظ إعدادات البريد الإلكتروني بنجاح', 'success');
            } else {
                showNotification(result.message, 'error');
            }
        } catch (error) {
            showNotification('حدث خطأ في حفظ الإعدادات', 'error');
        }
    }

    function formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('ar-SA', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    }
</script>
{% endblock %}