# -*- coding: utf-8 -*-
"""
Cyber Shield - Location Tracker
متتبع المواقع الجغرافية
"""

import requests
import logging
import json
import socket
from typing import Dict, List, Optional, Tuple
from datetime import datetime
import threading
import time

from src.database.db_manager import DatabaseManager
from config.settings import *

logger = logging.getLogger(__name__)

class LocationTracker:
    """
    متتبع المواقع الجغرافية وعناوين IP
    """
    
    def __init__(self):
        self.db_manager = DatabaseManager()
        self.location_cache = {}  # تخزين مؤقت للمواقع
        self.cache_expiry = 3600  # ساعة واحدة
        
        # خدمات تحديد الموقع
        self.geolocation_services = [
            "http://ip-api.com/json/",
            "https://ipapi.co/{}/json/",
            "https://freegeoip.app/json/"
        ]
        
        # إحصائيات التتبع
        self.tracking_stats = {
            'total_lookups': 0,
            'successful_lookups': 0,
            'cached_lookups': 0,
            'failed_lookups': 0
        }
    
    def get_current_location(self) -> Optional[Dict]:
        """
        الحصول على الموقع الحالي للمستخدم
        """
        try:
            # الحصول على IP العام
            public_ip = self.get_public_ip()
            if not public_ip:
                return None
            
            # الحصول على معلومات الموقع
            location_data = self.get_ip_location(public_ip)
            
            if location_data:
                # حفظ الموقع الحالي
                self._save_current_location(location_data)
                
            return location_data
            
        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على الموقع الحالي: {e}")
            return None
    
    def get_public_ip(self) -> Optional[str]:
        """
        الحصول على عنوان IP العام
        """
        try:
            # خدمات للحصول على IP العام
            ip_services = [
                "https://api.ipify.org",
                "https://icanhazip.com",
                "https://ident.me"
            ]
            
            for service in ip_services:
                try:
                    response = requests.get(service, timeout=5)
                    if response.status_code == 200:
                        ip = response.text.strip()
                        if self._is_valid_ip(ip):
                            return ip
                except:
                    continue
            
            return None
            
        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على IP العام: {e}")
            return None
    
    def get_ip_location(self, ip_address: str, use_cache: bool = True) -> Optional[Dict]:
        """
        الحصول على موقع عنوان IP
        """
        try:
            self.tracking_stats['total_lookups'] += 1
            
            # التحقق من التخزين المؤقت
            if use_cache and ip_address in self.location_cache:
                cache_entry = self.location_cache[ip_address]
                if time.time() - cache_entry['timestamp'] < self.cache_expiry:
                    self.tracking_stats['cached_lookups'] += 1
                    return cache_entry['data']
            
            # التحقق من كون IP محلي
            if self._is_private_ip(ip_address):
                return {
                    'ip': ip_address,
                    'country': 'Local',
                    'country_code': 'LO',
                    'region': 'Local Network',
                    'city': 'Local',
                    'latitude': 0.0,
                    'longitude': 0.0,
                    'isp': 'Local Network',
                    'org': 'Private Network',
                    'timezone': 'Local'
                }
            
            # محاولة الحصول على الموقع من الخدمات المختلفة
            location_data = None
            for service_url in self.geolocation_services:
                try:
                    location_data = self._query_geolocation_service(service_url, ip_address)
                    if location_data:
                        break
                except Exception as e:
                    logger.debug(f"فشل في الخدمة {service_url}: {e}")
                    continue
            
            if location_data:
                # حفظ في التخزين المؤقت
                self.location_cache[ip_address] = {
                    'data': location_data,
                    'timestamp': time.time()
                }
                
                self.tracking_stats['successful_lookups'] += 1
                
                # حفظ في قاعدة البيانات
                self._save_location_lookup(ip_address, location_data)
                
                return location_data
            else:
                self.tracking_stats['failed_lookups'] += 1
                return None
                
        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على موقع IP {ip_address}: {e}")
            self.tracking_stats['failed_lookups'] += 1
            return None
    
    def _query_geolocation_service(self, service_url: str, ip_address: str) -> Optional[Dict]:
        """
        استعلام خدمة تحديد الموقع الجغرافي
        """
        try:
            # تنسيق URL الخدمة
            if "{}" in service_url:
                url = service_url.format(ip_address)
            else:
                url = service_url + ip_address
            
            response = requests.get(url, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                
                # تحويل البيانات لتنسيق موحد
                return self._normalize_location_data(data, service_url)
            
            return None
            
        except Exception as e:
            logger.debug(f"خطأ في استعلام {service_url}: {e}")
            return None
    
    def _normalize_location_data(self, data: Dict, service_url: str) -> Dict:
        """
        توحيد تنسيق بيانات الموقع من خدمات مختلفة
        """
        try:
            # تحديد نوع الخدمة
            if "ip-api.com" in service_url:
                return self._normalize_ip_api_data(data)
            elif "ipapi.co" in service_url:
                return self._normalize_ipapi_data(data)
            elif "freegeoip.app" in service_url:
                return self._normalize_freegeoip_data(data)
            else:
                return data
                
        except Exception as e:
            logger.debug(f"خطأ في توحيد البيانات: {e}")
            return data
    
    def _normalize_ip_api_data(self, data: Dict) -> Dict:
        """
        توحيد بيانات ip-api.com
        """
        return {
            'ip': data.get('query'),
            'country': data.get('country'),
            'country_code': data.get('countryCode'),
            'region': data.get('regionName'),
            'city': data.get('city'),
            'latitude': data.get('lat'),
            'longitude': data.get('lon'),
            'isp': data.get('isp'),
            'org': data.get('org'),
            'timezone': data.get('timezone'),
            'zip_code': data.get('zip'),
            'as_number': data.get('as')
        }
    
    def _normalize_ipapi_data(self, data: Dict) -> Dict:
        """
        توحيد بيانات ipapi.co
        """
        return {
            'ip': data.get('ip'),
            'country': data.get('country_name'),
            'country_code': data.get('country_code'),
            'region': data.get('region'),
            'city': data.get('city'),
            'latitude': data.get('latitude'),
            'longitude': data.get('longitude'),
            'isp': data.get('org'),
            'org': data.get('org'),
            'timezone': data.get('timezone'),
            'zip_code': data.get('postal')
        }
    
    def _normalize_freegeoip_data(self, data: Dict) -> Dict:
        """
        توحيد بيانات freegeoip.app
        """
        return {
            'ip': data.get('ip'),
            'country': data.get('country_name'),
            'country_code': data.get('country_code'),
            'region': data.get('region_name'),
            'city': data.get('city'),
            'latitude': data.get('latitude'),
            'longitude': data.get('longitude'),
            'isp': 'Unknown',
            'org': 'Unknown',
            'timezone': data.get('time_zone'),
            'zip_code': data.get('zip_code')
        }
    
    def track_connection_location(self, ip_address: str, connection_info: Dict = None) -> Optional[Dict]:
        """
        تتبع موقع اتصال شبكي
        """
        try:
            location_data = self.get_ip_location(ip_address)
            
            if location_data:
                # إضافة معلومات الاتصال
                tracking_record = {
                    'ip_address': ip_address,
                    'location_data': location_data,
                    'connection_info': connection_info,
                    'tracked_at': datetime.now()
                }
                
                # حفظ سجل التتبع
                self._save_tracking_record(tracking_record)
                
                return tracking_record
            
            return None
            
        except Exception as e:
            logger.error(f"❌ خطأ في تتبع موقع الاتصال: {e}")
            return None
    
    def get_location_history(self, limit: int = 100) -> List[Dict]:
        """
        الحصول على تاريخ المواقع
        """
        try:
            query = """
                SELECT * FROM activity_logs 
                WHERE action LIKE '%LOCATION%' 
                ORDER BY created_at DESC 
                LIMIT ?
            """
            return self.db_manager.execute_query(query, (limit,))
        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على تاريخ المواقع: {e}")
            return []
    
    def get_ip_statistics(self) -> Dict:
        """
        الحصول على إحصائيات عناوين IP
        """
        try:
            stats = {
                'unique_ips': 0,
                'countries': {},
                'cities': {},
                'isps': {},
                'recent_locations': []
            }
            
            # استعلام قاعدة البيانات للإحصائيات
            query = """
                SELECT location_data FROM network_monitoring 
                WHERE location_data IS NOT NULL 
                ORDER BY detected_at DESC 
                LIMIT 1000
            """
            
            results = self.db_manager.execute_query(query)
            unique_ips = set()
            
            for row in results:
                try:
                    location_data = json.loads(row['location_data'])
                    ip = location_data.get('ip')
                    
                    if ip:
                        unique_ips.add(ip)
                        
                        # إحصائيات البلدان
                        country = location_data.get('country', 'Unknown')
                        stats['countries'][country] = stats['countries'].get(country, 0) + 1
                        
                        # إحصائيات المدن
                        city = location_data.get('city', 'Unknown')
                        stats['cities'][city] = stats['cities'].get(city, 0) + 1
                        
                        # إحصائيات مقدمي الخدمة
                        isp = location_data.get('isp', 'Unknown')
                        stats['isps'][isp] = stats['isps'].get(isp, 0) + 1
                        
                except:
                    continue
            
            stats['unique_ips'] = len(unique_ips)
            stats.update(self.tracking_stats)
            
            return stats
            
        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على إحصائيات IP: {e}")
            return {}
    
    def generate_location_map_data(self, limit: int = 100) -> List[Dict]:
        """
        إنشاء بيانات الخريطة للمواقع
        """
        try:
            map_data = []
            
            query = """
                SELECT location_data, COUNT(*) as count 
                FROM network_monitoring 
                WHERE location_data IS NOT NULL 
                GROUP BY location_data 
                ORDER BY count DESC 
                LIMIT ?
            """
            
            results = self.db_manager.execute_query(query, (limit,))
            
            for row in results:
                try:
                    location_data = json.loads(row['location_data'])
                    
                    if location_data.get('latitude') and location_data.get('longitude'):
                        map_point = {
                            'latitude': float(location_data['latitude']),
                            'longitude': float(location_data['longitude']),
                            'country': location_data.get('country', 'Unknown'),
                            'city': location_data.get('city', 'Unknown'),
                            'ip': location_data.get('ip', 'Unknown'),
                            'count': row['count'],
                            'info': f"{location_data.get('city', 'Unknown')}, {location_data.get('country', 'Unknown')}"
                        }
                        map_data.append(map_point)
                        
                except:
                    continue
            
            return map_data
            
        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء بيانات الخريطة: {e}")
            return []
    
    def _is_valid_ip(self, ip_address: str) -> bool:
        """
        التحقق من صحة عنوان IP
        """
        try:
            socket.inet_aton(ip_address)
            return True
        except socket.error:
            return False
    
    def _is_private_ip(self, ip_address: str) -> bool:
        """
        التحقق من كون IP محلي
        """
        try:
            import ipaddress
            ip = ipaddress.ip_address(ip_address)
            return ip.is_private or ip.is_loopback or ip.is_link_local
        except:
            return False
    
    def _save_current_location(self, location_data: Dict):
        """
        حفظ الموقع الحالي
        """
        try:
            self.db_manager.log_activity(
                user_id=None,
                action='CURRENT_LOCATION_UPDATED',
                description=f"تم تحديث الموقع الحالي: {location_data.get('city', 'Unknown')}, {location_data.get('country', 'Unknown')}",
                additional_data=location_data
            )
        except Exception as e:
            logger.error(f"❌ خطأ في حفظ الموقع الحالي: {e}")
    
    def _save_location_lookup(self, ip_address: str, location_data: Dict):
        """
        حفظ استعلام الموقع
        """
        try:
            self.db_manager.log_activity(
                user_id=None,
                action='IP_LOCATION_LOOKUP',
                description=f"استعلام موقع IP: {ip_address} -> {location_data.get('city', 'Unknown')}, {location_data.get('country', 'Unknown')}",
                ip_address=ip_address,
                additional_data=location_data
            )
        except Exception as e:
            logger.error(f"❌ خطأ في حفظ استعلام الموقع: {e}")
    
    def _save_tracking_record(self, tracking_record: Dict):
        """
        حفظ سجل التتبع
        """
        try:
            self.db_manager.log_activity(
                user_id=None,
                action='CONNECTION_LOCATION_TRACKED',
                description=f"تتبع موقع اتصال: {tracking_record['ip_address']}",
                ip_address=tracking_record['ip_address'],
                additional_data=tracking_record
            )
        except Exception as e:
            logger.error(f"❌ خطأ في حفظ سجل التتبع: {e}")
    
    def clear_location_cache(self):
        """
        مسح التخزين المؤقت للمواقع
        """
        self.location_cache.clear()
        logger.info("🧹 تم مسح التخزين المؤقت للمواقع")
    
    def get_tracking_statistics(self) -> Dict:
        """
        الحصول على إحصائيات التتبع
        """
        return self.tracking_stats.copy()
