# -*- coding: utf-8 -*-
"""
Cyber Shield - Setup Script
سكريبت إعداد وبناء التطبيق
"""

import sys
import os
from pathlib import Path
from setuptools import setup, find_packages

# قراءة معلومات التطبيق
sys.path.insert(0, str(Path(__file__).parent))
from config.settings import APP_NAME, APP_VERSION, APP_AUTHOR, APP_DESCRIPTION

# قراءة المتطلبات
def read_requirements():
    """قراءة ملف المتطلبات"""
    requirements_file = Path("requirements.txt")
    if requirements_file.exists():
        with open(requirements_file, 'r', encoding='utf-8') as f:
            return [line.strip() for line in f if line.strip() and not line.startswith('#')]
    return []

# قراءة الوصف الطويل
def read_long_description():
    """قراءة الوصف الطويل من README"""
    readme_file = Path("README.md")
    if readme_file.exists():
        with open(readme_file, 'r', encoding='utf-8') as f:
            return f.read()
    return APP_DESCRIPTION

# إعداد البناء
setup(
    name=APP_NAME.lower().replace(' ', '-'),
    version=APP_VERSION,
    author=APP_AUTHOR,
    author_email="<EMAIL>",
    description=APP_DESCRIPTION,
    long_description=read_long_description(),
    long_description_content_type="text/markdown",
    url="https://github.com/cyper-member/cyber-shield",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: End Users/Desktop",
        "Topic :: Security",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Operating System :: Microsoft :: Windows",
    ],
    python_requires=">=3.8",
    install_requires=read_requirements(),
    entry_points={
        "console_scripts": [
            "cyber-shield=main:main",
        ],
    },
    include_package_data=True,
    package_data={
        "": ["*.txt", "*.md", "*.json", "*.sql", "*.ico", "*.png"],
        "config": ["*.py", "*.json"],
        "assets": ["*"],
        "database": ["*.sql"],
    },
    zip_safe=False,
    keywords="cybersecurity antivirus security protection palestine",
    project_urls={
        "Bug Reports": "https://github.com/cyper-member/cyber-shield/issues",
        "Source": "https://github.com/cyper-member/cyber-shield",
        "Documentation": "https://github.com/cyper-member/cyber-shield/wiki",
    },
)
