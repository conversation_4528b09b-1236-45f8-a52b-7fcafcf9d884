# -*- coding: utf-8 -*-
"""
Cyber Shield - Admin Panel
لوحة تحكم المسؤول الرئيسية
"""

import logging
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta

from src.admin.user_manager import UserManager
from src.admin.logs_manager import LogsManager
from src.security.security_manager import SecurityManager
from src.utils.location_tracker import LocationTracker
from src.utils.map_generator import MapGenerator
from src.database.db_manager import DatabaseManager
from src.auth.permissions import Permission, require_permission, security_context
from config.settings import *

logger = logging.getLogger(__name__)

class AdminPanel:
    """
    لوحة تحكم المسؤول الرئيسية
    """
    
    def __init__(self):
        self.user_manager = UserManager()
        self.logs_manager = LogsManager()
        self.security_manager = SecurityManager()
        self.location_tracker = LocationTracker()
        self.map_generator = MapGenerator()
        self.db_manager = DatabaseManager()
    
    @require_permission(Permission.ADMIN_PANEL)
    def get_dashboard_data(self, user_data: Dict) -> Dict:
        """
        الحصول على بيانات لوحة التحكم الرئيسية
        """
        try:
            dashboard = {
                'overview': self._get_system_overview(),
                'recent_activities': self._get_recent_activities(20),
                'security_alerts': self._get_security_alerts(10),
                'user_statistics': self._get_user_statistics(),
                'system_health': self._get_system_health(),
                'network_status': self._get_network_status(),
                'threat_summary': self._get_threat_summary(),
                'performance_metrics': self._get_performance_metrics()
            }
            
            return dashboard
            
        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على بيانات لوحة التحكم: {e}")
            return {}
    
    @require_permission(Permission.MANAGE_USERS)
    def manage_users(self, user_data: Dict, action: str, **kwargs) -> Tuple[bool, str, Optional[Dict]]:
        """
        إدارة المستخدمين
        """
        try:
            if action == 'list':
                users = self.user_manager.get_all_users(user_data)
                return True, "تم الحصول على قائمة المستخدمين", users
            
            elif action == 'get':
                user_id = kwargs.get('user_id')
                user = self.user_manager.get_user_by_id(user_data, user_id)
                if user:
                    return True, "تم الحصول على بيانات المستخدم", user
                else:
                    return False, "المستخدم غير موجود", None
            
            elif action == 'create':
                success, message = self.user_manager.create_user(user_data, kwargs.get('user_data', {}))
                return success, message, None
            
            elif action == 'update':
                user_id = kwargs.get('user_id')
                update_data = kwargs.get('update_data', {})
                success, message = self.user_manager.update_user(user_data, user_id, update_data)
                return success, message, None
            
            elif action == 'delete':
                user_id = kwargs.get('user_id')
                success, message = self.user_manager.delete_user(user_data, user_id)
                return success, message, None
            
            elif action == 'lock':
                user_id = kwargs.get('user_id')
                duration = kwargs.get('duration_hours', 24)
                success, message = self.user_manager.lock_user(user_data, user_id, duration)
                return success, message, None
            
            elif action == 'unlock':
                user_id = kwargs.get('user_id')
                success, message = self.user_manager.unlock_user(user_data, user_id)
                return success, message, None
            
            else:
                return False, f"إجراء غير مدعوم: {action}", None
                
        except Exception as e:
            logger.error(f"❌ خطأ في إدارة المستخدمين: {e}")
            return False, "حدث خطأ في إدارة المستخدمين", None
    
    @require_permission(Permission.VIEW_ALL_LOGS)
    def manage_logs(self, user_data: Dict, action: str, **kwargs) -> Tuple[bool, str, Optional[Dict]]:
        """
        إدارة السجلات
        """
        try:
            if action == 'activity':
                filters = kwargs.get('filters', {})
                limit = kwargs.get('limit', 100)
                logs = self.logs_manager.get_activity_logs(user_data, filters, limit)
                return True, "تم الحصول على سجلات الأنشطة", logs
            
            elif action == 'security':
                filters = kwargs.get('filters', {})
                limit = kwargs.get('limit', 100)
                logs = self.logs_manager.get_security_logs(user_data, filters, limit)
                return True, "تم الحصول على السجلات الأمنية", logs
            
            elif action == 'login':
                filters = kwargs.get('filters', {})
                limit = kwargs.get('limit', 100)
                logs = self.logs_manager.get_login_logs(user_data, filters, limit)
                return True, "تم الحصول على سجلات تسجيل الدخول", logs
            
            elif action == 'statistics':
                stats = self.logs_manager.get_system_statistics(user_data)
                return True, "تم الحصول على الإحصائيات", stats
            
            elif action == 'export':
                log_type = kwargs.get('log_type', 'activity')
                format_type = kwargs.get('format', 'csv')
                filters = kwargs.get('filters', {})
                file_path = self.logs_manager.export_logs(user_data, log_type, format_type, filters)
                if file_path:
                    return True, f"تم تصدير السجلات: {file_path}", {'file_path': file_path}
                else:
                    return False, "فشل في تصدير السجلات", None
            
            else:
                return False, f"إجراء غير مدعوم: {action}", None
                
        except Exception as e:
            logger.error(f"❌ خطأ في إدارة السجلات: {e}")
            return False, "حدث خطأ في إدارة السجلات", None
    
    @require_permission(Permission.SYSTEM_CONTROL)
    def manage_security(self, user_data: Dict, action: str, **kwargs) -> Tuple[bool, str, Optional[Dict]]:
        """
        إدارة الأمان
        """
        try:
            if action == 'status':
                dashboard = self.security_manager.get_security_dashboard()
                return True, "تم الحصول على حالة الأمان", dashboard
            
            elif action == 'start_protection':
                success = self.security_manager.start_protection()
                if success:
                    return True, "تم بدء أنظمة الحماية", None
                else:
                    return False, "فشل في بدء أنظمة الحماية", None
            
            elif action == 'stop_protection':
                success = self.security_manager.stop_protection()
                if success:
                    return True, "تم إيقاف أنظمة الحماية", None
                else:
                    return False, "فشل في إيقاف أنظمة الحماية", None
            
            elif action == 'full_scan':
                callback = kwargs.get('callback')
                success = self.security_manager.start_full_scan(callback)
                if success:
                    return True, "تم بدء الفحص الشامل", None
                else:
                    return False, "فشل في بدء الفحص الشامل", None
            
            elif action == 'quick_scan':
                callback = kwargs.get('callback')
                success = self.security_manager.start_quick_scan(callback)
                if success:
                    return True, "تم بدء الفحص السريع", None
                else:
                    return False, "فشل في بدء الفحص السريع", None
            
            elif action == 'set_security_level':
                level = kwargs.get('level', 'MEDIUM')
                success = self.security_manager.set_security_level(level)
                if success:
                    return True, f"تم تعيين مستوى الأمان إلى {level}", None
                else:
                    return False, "فشل في تعيين مستوى الأمان", None
            
            elif action == 'block_ip':
                ip_address = kwargs.get('ip_address')
                reason = kwargs.get('reason', 'محظور من لوحة الإدارة')
                success = self.security_manager.block_ip_address(ip_address, reason)
                if success:
                    return True, f"تم حظر العنوان {ip_address}", None
                else:
                    return False, f"فشل في حظر العنوان {ip_address}", None
            
            elif action == 'quarantine_file':
                file_path = kwargs.get('file_path')
                reason = kwargs.get('reason', 'عزل من لوحة الإدارة')
                success = self.security_manager.quarantine_file(file_path, reason)
                if success:
                    return True, f"تم عزل الملف {file_path}", None
                else:
                    return False, f"فشل في عزل الملف {file_path}", None
            
            elif action == 'threats':
                limit = kwargs.get('limit', 100)
                threats = self.security_manager.get_recent_threats(limit)
                return True, "تم الحصول على التهديدات", threats
            
            else:
                return False, f"إجراء غير مدعوم: {action}", None
                
        except Exception as e:
            logger.error(f"❌ خطأ في إدارة الأمان: {e}")
            return False, "حدث خطأ في إدارة الأمان", None
    
    @require_permission(Permission.VIEW_ALL_LOGS)
    def manage_location_tracking(self, user_data: Dict, action: str, **kwargs) -> Tuple[bool, str, Optional[Dict]]:
        """
        إدارة تتبع المواقع
        """
        try:
            if action == 'current_location':
                location = self.location_tracker.get_current_location()
                if location:
                    return True, "تم الحصول على الموقع الحالي", location
                else:
                    return False, "فشل في الحصول على الموقع الحالي", None
            
            elif action == 'ip_location':
                ip_address = kwargs.get('ip_address')
                location = self.location_tracker.get_ip_location(ip_address)
                if location:
                    return True, f"تم الحصول على موقع {ip_address}", location
                else:
                    return False, f"فشل في الحصول على موقع {ip_address}", None
            
            elif action == 'statistics':
                stats = self.location_tracker.get_ip_statistics()
                return True, "تم الحصول على إحصائيات المواقع", stats
            
            elif action == 'history':
                limit = kwargs.get('limit', 100)
                history = self.location_tracker.get_location_history(limit)
                return True, "تم الحصول على تاريخ المواقع", history
            
            elif action == 'generate_map':
                map_type = kwargs.get('map_type', 'connections')
                limit = kwargs.get('limit', 100)
                
                if map_type == 'connections':
                    map_file = self.map_generator.generate_connections_map(limit)
                elif map_type == 'threats':
                    map_file = self.map_generator.generate_threats_map(limit)
                elif map_type == 'live':
                    map_file = self.map_generator.generate_live_map()
                else:
                    return False, f"نوع خريطة غير مدعوم: {map_type}", None
                
                if map_file:
                    return True, f"تم إنشاء الخريطة: {map_file}", {'map_file': map_file}
                else:
                    return False, "فشل في إنشاء الخريطة", None
            
            elif action == 'open_map':
                map_file = kwargs.get('map_file')
                self.map_generator.open_map_in_browser(map_file)
                return True, "تم فتح الخريطة في المتصفح", None
            
            else:
                return False, f"إجراء غير مدعوم: {action}", None
                
        except Exception as e:
            logger.error(f"❌ خطأ في إدارة تتبع المواقع: {e}")
            return False, "حدث خطأ في إدارة تتبع المواقع", None
    
    @require_permission(Permission.MANAGE_SETTINGS)
    def manage_settings(self, user_data: Dict, action: str, **kwargs) -> Tuple[bool, str, Optional[Dict]]:
        """
        إدارة إعدادات النظام
        """
        try:
            if action == 'get':
                settings = self._get_system_settings()
                return True, "تم الحصول على الإعدادات", settings
            
            elif action == 'update':
                settings_data = kwargs.get('settings', {})
                success = self._update_system_settings(user_data, settings_data)
                if success:
                    return True, "تم تحديث الإعدادات", None
                else:
                    return False, "فشل في تحديث الإعدادات", None
            
            elif action == 'reset':
                success = self._reset_system_settings(user_data)
                if success:
                    return True, "تم إعادة تعيين الإعدادات", None
                else:
                    return False, "فشل في إعادة تعيين الإعدادات", None
            
            else:
                return False, f"إجراء غير مدعوم: {action}", None
                
        except Exception as e:
            logger.error(f"❌ خطأ في إدارة الإعدادات: {e}")
            return False, "حدث خطأ في إدارة الإعدادات", None
    
    def _get_system_overview(self) -> Dict:
        """
        الحصول على نظرة عامة على النظام
        """
        try:
            overview = {
                'uptime': self._get_system_uptime(),
                'protection_status': self.security_manager.is_protection_enabled,
                'security_level': self.security_manager.security_level,
                'total_users': 0,
                'active_sessions': 0,
                'total_threats': 0,
                'threats_today': 0,
                'system_health': 'GOOD'
            }
            
            # إحصائيات المستخدمين
            query = "SELECT COUNT(*) as count FROM users"
            result = self.db_manager.execute_query(query)
            overview['total_users'] = result[0]['count'] if result else 0
            
            # الجلسات النشطة
            query = """
                SELECT COUNT(*) as count FROM sessions 
                WHERE is_active = TRUE AND expires_at > CURRENT_TIMESTAMP
            """
            result = self.db_manager.execute_query(query)
            overview['active_sessions'] = result[0]['count'] if result else 0
            
            # إجمالي التهديدات
            query = "SELECT COUNT(*) as count FROM detected_threats"
            result = self.db_manager.execute_query(query)
            overview['total_threats'] = result[0]['count'] if result else 0
            
            # التهديدات اليوم
            query = """
                SELECT COUNT(*) as count FROM detected_threats 
                WHERE DATE(detected_at) = DATE('now')
            """
            result = self.db_manager.execute_query(query)
            overview['threats_today'] = result[0]['count'] if result else 0
            
            return overview
            
        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على نظرة عامة: {e}")
            return {}
    
    def _get_recent_activities(self, limit: int = 20) -> List[Dict]:
        """
        الحصول على الأنشطة الأخيرة
        """
        try:
            query = """
                SELECT al.*, u.username 
                FROM activity_logs al
                LEFT JOIN users u ON al.user_id = u.id
                ORDER BY al.created_at DESC 
                LIMIT ?
            """
            return self.db_manager.execute_query(query, (limit,))
        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على الأنشطة الأخيرة: {e}")
            return []
    
    def _get_security_alerts(self, limit: int = 10) -> List[Dict]:
        """
        الحصول على التنبيهات الأمنية
        """
        try:
            query = """
                SELECT * FROM detected_threats 
                WHERE severity IN ('HIGH', 'CRITICAL')
                ORDER BY detected_at DESC 
                LIMIT ?
            """
            return self.db_manager.execute_query(query, (limit,))
        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على التنبيهات الأمنية: {e}")
            return []
    
    def _get_user_statistics(self) -> Dict:
        """
        إحصائيات المستخدمين
        """
        return self.logs_manager._get_users_statistics()
    
    def _get_system_health(self) -> Dict:
        """
        صحة النظام
        """
        return self.security_manager._get_system_health()
    
    def _get_network_status(self) -> Dict:
        """
        حالة الشبكة
        """
        return self.logs_manager._get_network_statistics()
    
    def _get_threat_summary(self) -> Dict:
        """
        ملخص التهديدات
        """
        return self.logs_manager._get_security_statistics()
    
    def _get_performance_metrics(self) -> Dict:
        """
        مقاييس الأداء
        """
        try:
            import psutil
            
            return {
                'cpu_usage': psutil.cpu_percent(interval=1),
                'memory_usage': psutil.virtual_memory().percent,
                'disk_usage': psutil.disk_usage('C:' if os.name == 'nt' else '/').percent,
                'network_io': psutil.net_io_counters()._asdict(),
                'process_count': len(psutil.pids())
            }
        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على مقاييس الأداء: {e}")
            return {}
    
    def _get_system_uptime(self) -> str:
        """
        وقت تشغيل النظام
        """
        try:
            import psutil
            boot_time = datetime.fromtimestamp(psutil.boot_time())
            uptime = datetime.now() - boot_time
            
            days = uptime.days
            hours, remainder = divmod(uptime.seconds, 3600)
            minutes, _ = divmod(remainder, 60)
            
            return f"{days} يوم، {hours} ساعة، {minutes} دقيقة"
        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على وقت التشغيل: {e}")
            return "غير معروف"

    def _get_system_settings(self) -> Dict:
        """
        الحصول على إعدادات النظام
        """
        try:
            query = "SELECT * FROM settings ORDER BY category, key"
            settings_rows = self.db_manager.execute_query(query)

            settings = {}
            for row in settings_rows:
                category = row['category']
                if category not in settings:
                    settings[category] = {}

                settings[category][row['key']] = {
                    'value': row['value'],
                    'description': row['description'],
                    'updated_at': row['updated_at']
                }

            return settings

        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على الإعدادات: {e}")
            return {}

    def _update_system_settings(self, user_data: Dict, settings_data: Dict) -> bool:
        """
        تحديث إعدادات النظام
        """
        try:
            for category, category_settings in settings_data.items():
                for key, value in category_settings.items():
                    query = """
                        UPDATE settings
                        SET value = ?, updated_at = CURRENT_TIMESTAMP, updated_by = ?
                        WHERE key = ? AND category = ?
                    """
                    self.db_manager.execute_query(query, (value, user_data['id'], key, category))

            # تسجيل النشاط
            self.db_manager.log_activity(
                user_data['id'],
                'SETTINGS_UPDATED',
                'تم تحديث إعدادات النظام',
                severity='INFO'
            )

            return True

        except Exception as e:
            logger.error(f"❌ خطأ في تحديث الإعدادات: {e}")
            return False

    def _reset_system_settings(self, user_data: Dict) -> bool:
        """
        إعادة تعيين إعدادات النظام للقيم الافتراضية
        """
        try:
            # حذف الإعدادات الحالية
            query = "DELETE FROM settings"
            self.db_manager.execute_query(query)

            # إعادة إدراج الإعدادات الافتراضية
            self.db_manager._insert_default_settings(self.db_manager.get_connection().cursor())
            self.db_manager.get_connection().commit()

            # تسجيل النشاط
            self.db_manager.log_activity(
                user_data['id'],
                'SETTINGS_RESET',
                'تم إعادة تعيين إعدادات النظام للقيم الافتراضية',
                severity='WARNING'
            )

            return True

        except Exception as e:
            logger.error(f"❌ خطأ في إعادة تعيين الإعدادات: {e}")
            return False

    @require_permission(Permission.EMERGENCY_SHUTDOWN)
    def emergency_shutdown(self, user_data: Dict, reason: str = "إغلاق طارئ") -> bool:
        """
        الإغلاق الطارئ للنظام
        """
        try:
            # إيقاف جميع أنظمة الحماية
            self.security_manager.stop_protection()

            # إلغاء تفعيل جميع الجلسات
            from src.auth.session_manager import SessionManager
            session_manager = SessionManager()

            # الحصول على جميع الجلسات النشطة
            query = "SELECT DISTINCT user_id FROM sessions WHERE is_active = TRUE"
            active_users = self.db_manager.execute_query(query)

            for user in active_users:
                session_manager.invalidate_user_sessions(user['user_id'])

            # تسجيل الإغلاق الطارئ
            self.db_manager.log_activity(
                user_data['id'],
                'EMERGENCY_SHUTDOWN',
                f'تم تنفيذ إغلاق طارئ للنظام - السبب: {reason}',
                severity='CRITICAL'
            )

            logger.critical(f"🚨 تم تنفيذ إغلاق طارئ للنظام بواسطة {user_data['username']}")

            return True

        except Exception as e:
            logger.error(f"❌ خطأ في الإغلاق الطارئ: {e}")
            return False

    @require_permission(Permission.BACKUP_RESTORE)
    def backup_system(self, user_data: Dict) -> Tuple[bool, str]:
        """
        نسخ احتياطي للنظام
        """
        try:
            import shutil
            import zipfile
            from datetime import datetime

            # إنشاء مجلد النسخ الاحتياطية
            backup_dir = TEMP_DIR / "backups"
            backup_dir.mkdir(exist_ok=True)

            # اسم ملف النسخة الاحتياطية
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_filename = f"cyber_shield_backup_{timestamp}.zip"
            backup_path = backup_dir / backup_filename

            # إنشاء النسخة الاحتياطية
            with zipfile.ZipFile(backup_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                # نسخ قاعدة البيانات
                if DATABASE_PATH.exists():
                    zipf.write(DATABASE_PATH, "database/cyber_shield.db")

                # نسخ ملفات التكوين
                config_dir = Path("config")
                if config_dir.exists():
                    for file_path in config_dir.rglob("*"):
                        if file_path.is_file():
                            zipf.write(file_path, f"config/{file_path.relative_to(config_dir)}")

                # نسخ السجلات
                if LOGS_DIR.exists():
                    for file_path in LOGS_DIR.rglob("*.log"):
                        if file_path.is_file():
                            zipf.write(file_path, f"logs/{file_path.name}")

            # تسجيل النشاط
            self.db_manager.log_activity(
                user_data['id'],
                'SYSTEM_BACKUP',
                f'تم إنشاء نسخة احتياطية: {backup_filename}',
                severity='INFO'
            )

            logger.info(f"✅ تم إنشاء نسخة احتياطية: {backup_path}")
            return True, str(backup_path)

        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء النسخة الاحتياطية: {e}")
            return False, "فشل في إنشاء النسخة الاحتياطية"

    @require_permission(Permission.BACKUP_RESTORE)
    def restore_system(self, user_data: Dict, backup_file: str) -> Tuple[bool, str]:
        """
        استعادة النظام من نسخة احتياطية
        """
        try:
            import zipfile
            import shutil

            if not Path(backup_file).exists():
                return False, "ملف النسخة الاحتياطية غير موجود"

            # إنشاء مجلد مؤقت للاستعادة
            restore_dir = TEMP_DIR / "restore"
            if restore_dir.exists():
                shutil.rmtree(restore_dir)
            restore_dir.mkdir()

            # استخراج النسخة الاحتياطية
            with zipfile.ZipFile(backup_file, 'r') as zipf:
                zipf.extractall(restore_dir)

            # استعادة قاعدة البيانات
            backup_db = restore_dir / "database" / "cyber_shield.db"
            if backup_db.exists():
                # إنشاء نسخة احتياطية من قاعدة البيانات الحالية
                current_db_backup = DATABASE_PATH.with_suffix('.db.backup')
                if DATABASE_PATH.exists():
                    shutil.copy2(DATABASE_PATH, current_db_backup)

                # استعادة قاعدة البيانات
                shutil.copy2(backup_db, DATABASE_PATH)

            # استعادة ملفات التكوين
            backup_config = restore_dir / "config"
            if backup_config.exists():
                config_dir = Path("config")
                for file_path in backup_config.rglob("*"):
                    if file_path.is_file():
                        target_path = config_dir / file_path.relative_to(backup_config)
                        target_path.parent.mkdir(parents=True, exist_ok=True)
                        shutil.copy2(file_path, target_path)

            # تنظيف المجلد المؤقت
            shutil.rmtree(restore_dir)

            # تسجيل النشاط
            self.db_manager.log_activity(
                user_data['id'],
                'SYSTEM_RESTORE',
                f'تم استعادة النظام من: {Path(backup_file).name}',
                severity='WARNING'
            )

            logger.warning(f"⚠️ تم استعادة النظام من: {backup_file}")
            return True, "تم استعادة النظام بنجاح"

        except Exception as e:
            logger.error(f"❌ خطأ في استعادة النظام: {e}")
            return False, "فشل في استعادة النظام"

    def get_admin_statistics(self) -> Dict:
        """
        الحصول على إحصائيات شاملة للمدير
        """
        try:
            stats = {
                'system': self._get_system_overview(),
                'users': self._get_user_statistics(),
                'security': self._get_threat_summary(),
                'network': self._get_network_status(),
                'performance': self._get_performance_metrics(),
                'activities': self.logs_manager._get_activities_statistics(),
                'files': self.logs_manager._get_files_statistics()
            }

            return stats

        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على إحصائيات المدير: {e}")
            return {}
