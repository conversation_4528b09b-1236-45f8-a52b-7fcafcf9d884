<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الصفحة غير موجودة - Cyber Shield</title>
    
    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #000000 0%, #1a5f1a 50%, #ffffff 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .error-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            padding: 3rem;
            text-align: center;
            max-width: 600px;
            width: 100%;
            margin: 20px;
        }
        
        .error-icon {
            font-size: 6rem;
            color: #1a5f1a;
            margin-bottom: 1rem;
        }
        
        .error-code {
            font-size: 4rem;
            font-weight: 700;
            color: #000000;
            margin-bottom: 1rem;
        }
        
        .btn-home {
            background: linear-gradient(135deg, #1a5f1a, #2d8f2d);
            border: none;
            border-radius: 10px;
            padding: 15px 30px;
            font-size: 18px;
            font-weight: 600;
            color: white;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
        }
        
        .btn-home:hover {
            background: linear-gradient(135deg, #2d8f2d, #1a5f1a);
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(26, 95, 26, 0.3);
            color: white;
        }
        
        .flag-animation {
            animation: wave 2s ease-in-out infinite;
        }
        
        @keyframes wave {
            0%, 100% { transform: rotate(0deg); }
            25% { transform: rotate(5deg); }
            75% { transform: rotate(-5deg); }
        }
    </style>
</head>
<body>
    <div class="error-container">
        <div class="flag-animation mb-3">
            <span style="font-size: 3rem;">🇵🇸</span>
        </div>
        
        <div class="error-icon">
            <i class="fas fa-search"></i>
        </div>
        
        <div class="error-code">404</div>
        
        <h2 class="mb-3">الصفحة غير موجودة</h2>
        
        <p class="text-muted mb-4">
            عذراً، الصفحة التي تبحث عنها غير موجودة أو تم نقلها إلى موقع آخر.
        </p>
        
        <div class="mb-4">
            <a href="/" class="btn-home">
                <i class="fas fa-home me-2"></i>
                العودة للصفحة الرئيسية
            </a>
        </div>
        
        <hr class="my-4">
        
        <div class="text-center">
            <h5>
                <i class="fas fa-shield-alt text-success me-2"></i>
                Cyber Shield
            </h5>
            <p class="text-muted mb-0">
                🇵🇸 <strong>Cyper Member | State of Palestine</strong>
            </p>
            <small class="text-muted">
                صنع بـ ❤️ في فلسطين
            </small>
        </div>
    </div>
</body>
</html>
