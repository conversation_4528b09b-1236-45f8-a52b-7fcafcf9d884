# -*- coding: utf-8 -*-
"""
Cyber Shield - Threat Detector
كاشف التهديدات والبرمجيات الخبيثة
"""

import os
import re
import psutil
import hashlib
import logging
import threading
from typing import Dict, List, Optional, Set
from datetime import datetime
import json

from src.database.db_manager import DatabaseManager
from config.settings import *

logger = logging.getLogger(__name__)

class ThreatDetector:
    """
    كاشف التهديدات والبرمجيات الخبيثة
    """
    
    def __init__(self):
        self.db_manager = DatabaseManager()
        self.is_scanning = False
        self.scan_thread = None
        
        # قواعد بيانات التهديدات
        self.malware_signatures = self._load_malware_signatures()
        self.suspicious_processes = self._load_suspicious_processes()
        self.malicious_domains = self._load_malicious_domains()
        self.threat_patterns = self._load_threat_patterns()
        
        # إحصائيات الفحص
        self.scan_stats = {
            'files_scanned': 0,
            'threats_found': 0,
            'last_scan': None,
            'scan_duration': 0
        }
    
    def start_full_scan(self, callback=None):
        """
        بدء فحص شامل للنظام
        """
        if self.is_scanning:
            logger.warning("⚠️ الفحص قيد التشغيل بالفعل")
            return False
        
        self.is_scanning = True
        self.scan_thread = threading.Thread(
            target=self._full_scan_thread,
            args=(callback,),
            daemon=True
        )
        self.scan_thread.start()
        
        logger.info("🔍 بدء الفحص الشامل للنظام")
        return True
    
    def start_quick_scan(self, callback=None):
        """
        بدء فحص سريع
        """
        if self.is_scanning:
            logger.warning("⚠️ الفحص قيد التشغيل بالفعل")
            return False
        
        self.is_scanning = True
        self.scan_thread = threading.Thread(
            target=self._quick_scan_thread,
            args=(callback,),
            daemon=True
        )
        self.scan_thread.start()
        
        logger.info("⚡ بدء الفحص السريع")
        return True
    
    def stop_scan(self):
        """
        إيقاف الفحص
        """
        self.is_scanning = False
        if self.scan_thread:
            self.scan_thread.join(timeout=5)
        
        logger.info("🛑 تم إيقاف الفحص")
    
    def _full_scan_thread(self, callback=None):
        """
        خيط الفحص الشامل
        """
        try:
            start_time = datetime.now()
            self.scan_stats['files_scanned'] = 0
            self.scan_stats['threats_found'] = 0
            
            # فحص العمليات النشطة
            self._scan_running_processes(callback)
            
            # فحص ملفات النظام
            self._scan_system_files(callback)
            
            # فحص ملفات المستخدم
            self._scan_user_files(callback)
            
            # فحص التسجيل
            self._scan_registry(callback)
            
            # فحص الشبكة
            self._scan_network_connections(callback)
            
            # إنهاء الفحص
            end_time = datetime.now()
            self.scan_stats['scan_duration'] = (end_time - start_time).total_seconds()
            self.scan_stats['last_scan'] = end_time
            
            self.is_scanning = False
            
            if callback:
                callback('scan_completed', self.scan_stats)
            
            logger.info(f"✅ انتهى الفحص الشامل - فحص {self.scan_stats['files_scanned']} ملف، وجد {self.scan_stats['threats_found']} تهديد")
            
        except Exception as e:
            self.is_scanning = False
            logger.error(f"❌ خطأ في الفحص الشامل: {e}")
            if callback:
                callback('scan_error', str(e))
    
    def _quick_scan_thread(self, callback=None):
        """
        خيط الفحص السريع
        """
        try:
            start_time = datetime.now()
            self.scan_stats['files_scanned'] = 0
            self.scan_stats['threats_found'] = 0
            
            # فحص العمليات النشطة
            self._scan_running_processes(callback)
            
            # فحص المجلدات الحساسة
            critical_paths = [
                os.path.expanduser("~\\Downloads"),
                os.path.expanduser("~\\Desktop"),
                os.path.expanduser("~\\AppData\\Local\\Temp"),
                "C:\\Windows\\Temp"
            ]
            
            for path in critical_paths:
                if os.path.exists(path):
                    self._scan_directory(path, callback, max_depth=2)
            
            # إنهاء الفحص
            end_time = datetime.now()
            self.scan_stats['scan_duration'] = (end_time - start_time).total_seconds()
            self.scan_stats['last_scan'] = end_time
            
            self.is_scanning = False
            
            if callback:
                callback('scan_completed', self.scan_stats)
            
            logger.info(f"⚡ انتهى الفحص السريع - فحص {self.scan_stats['files_scanned']} ملف، وجد {self.scan_stats['threats_found']} تهديد")
            
        except Exception as e:
            self.is_scanning = False
            logger.error(f"❌ خطأ في الفحص السريع: {e}")
            if callback:
                callback('scan_error', str(e))
    
    def _scan_running_processes(self, callback=None):
        """
        فحص العمليات النشطة
        """
        try:
            for process in psutil.process_iter(['pid', 'name', 'exe', 'cmdline']):
                if not self.is_scanning:
                    break
                
                try:
                    process_info = process.info
                    if self._is_suspicious_process(process_info):
                        threat = self._create_threat_record(
                            'SUSPICIOUS_PROCESS',
                            f"عملية مشبوهة: {process_info['name']}",
                            process_name=process_info['name'],
                            file_path=process_info.get('exe'),
                            severity='HIGH'
                        )
                        
                        if callback:
                            callback('threat_found', threat)
                        
                        self.scan_stats['threats_found'] += 1
                        
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
                    
        except Exception as e:
            logger.error(f"❌ خطأ في فحص العمليات: {e}")
    
    def _scan_system_files(self, callback=None):
        """
        فحص ملفات النظام
        """
        system_paths = [
            "C:\\Windows\\System32",
            "C:\\Windows\\SysWOW64",
            "C:\\Program Files",
            "C:\\Program Files (x86)"
        ]
        
        for path in system_paths:
            if os.path.exists(path) and self.is_scanning:
                self._scan_directory(path, callback, max_depth=3)
    
    def _scan_user_files(self, callback=None):
        """
        فحص ملفات المستخدم
        """
        user_paths = [
            os.path.expanduser("~\\Downloads"),
            os.path.expanduser("~\\Desktop"),
            os.path.expanduser("~\\Documents"),
            os.path.expanduser("~\\AppData\\Local"),
            os.path.expanduser("~\\AppData\\Roaming")
        ]
        
        for path in user_paths:
            if os.path.exists(path) and self.is_scanning:
                self._scan_directory(path, callback, max_depth=4)
    
    def _scan_directory(self, directory: str, callback=None, max_depth: int = 5):
        """
        فحص مجلد
        """
        try:
            for root, dirs, files in os.walk(directory):
                if not self.is_scanning:
                    break
                
                # تحديد العمق
                depth = root.replace(directory, '').count(os.sep)
                if depth >= max_depth:
                    dirs.clear()
                    continue
                
                # تجاهل المجلدات المخفية والنظام
                dirs[:] = [d for d in dirs if not d.startswith('.') and d not in ['$RECYCLE.BIN', 'System Volume Information']]
                
                for file in files:
                    if not self.is_scanning:
                        break
                    
                    file_path = os.path.join(root, file)
                    self._scan_file(file_path, callback)
                    
        except (OSError, PermissionError) as e:
            logger.debug(f"لا يمكن الوصول للمجلد {directory}: {e}")
    
    def _scan_file(self, file_path: str, callback=None):
        """
        فحص ملف واحد
        """
        try:
            if not os.path.isfile(file_path):
                return
            
            self.scan_stats['files_scanned'] += 1
            
            # فحص الاسم والامتداد
            if self._is_suspicious_filename(file_path):
                threat = self._create_threat_record(
                    'SUSPICIOUS_FILE',
                    f"ملف مشبوه: {os.path.basename(file_path)}",
                    file_path=file_path,
                    severity='MEDIUM'
                )
                
                if callback:
                    callback('threat_found', threat)
                
                self.scan_stats['threats_found'] += 1
                return
            
            # فحص المحتوى للملفات الصغيرة
            file_size = os.path.getsize(file_path)
            if file_size < 10 * 1024 * 1024:  # أقل من 10 ميجابايت
                if self._scan_file_content(file_path):
                    threat = self._create_threat_record(
                        'MALWARE_SIGNATURE',
                        f"توقيع برمجية خبيثة في: {os.path.basename(file_path)}",
                        file_path=file_path,
                        severity='HIGH'
                    )
                    
                    if callback:
                        callback('threat_found', threat)
                    
                    self.scan_stats['threats_found'] += 1
            
            # تحديث التقدم
            if callback and self.scan_stats['files_scanned'] % 100 == 0:
                callback('scan_progress', {
                    'files_scanned': self.scan_stats['files_scanned'],
                    'threats_found': self.scan_stats['threats_found']
                })
                
        except (OSError, PermissionError):
            pass  # تجاهل الملفات التي لا يمكن الوصول إليها
        except Exception as e:
            logger.debug(f"خطأ في فحص الملف {file_path}: {e}")
    
    def _scan_file_content(self, file_path: str) -> bool:
        """
        فحص محتوى الملف للبحث عن توقيعات البرمجيات الخبيثة
        """
        try:
            with open(file_path, 'rb') as f:
                content = f.read(1024 * 1024)  # قراءة أول ميجابايت
                
                # فحص التوقيعات
                for signature in self.malware_signatures:
                    if signature.encode() in content:
                        return True
                
                # فحص الأنماط
                content_str = content.decode('utf-8', errors='ignore')
                for pattern in self.threat_patterns:
                    if re.search(pattern, content_str, re.IGNORECASE):
                        return True
                
                return False
                
        except Exception:
            return False
    
    def _scan_registry(self, callback=None):
        """
        فحص سجل النظام (Windows Registry)
        """
        # سيتم تطوير هذه الوظيفة لاحقاً
        pass
    
    def _scan_network_connections(self, callback=None):
        """
        فحص الاتصالات الشبكية
        """
        try:
            connections = psutil.net_connections()
            
            for conn in connections:
                if not self.is_scanning:
                    break
                
                if conn.raddr and self._is_malicious_domain(conn.raddr.ip):
                    threat = self._create_threat_record(
                        'MALICIOUS_CONNECTION',
                        f"اتصال بنطاق خبيث: {conn.raddr.ip}",
                        severity='HIGH',
                        additional_info={
                            'local_address': f"{conn.laddr.ip}:{conn.laddr.port}",
                            'remote_address': f"{conn.raddr.ip}:{conn.raddr.port}",
                            'status': conn.status
                        }
                    )
                    
                    if callback:
                        callback('threat_found', threat)
                    
                    self.scan_stats['threats_found'] += 1
                    
        except Exception as e:
            logger.error(f"❌ خطأ في فحص الاتصالات الشبكية: {e}")
    
    def _is_suspicious_process(self, process_info: Dict) -> bool:
        """
        التحقق من كون العملية مشبوهة
        """
        process_name = process_info.get('name', '').lower()
        
        # فحص قائمة العمليات المشبوهة
        if process_name in self.suspicious_processes:
            return True
        
        # فحص أنماط مشبوهة في اسم العملية
        suspicious_patterns = [
            r'.*virus.*', r'.*malware.*', r'.*trojan.*',
            r'.*keylog.*', r'.*backdoor.*', r'.*rootkit.*'
        ]
        
        for pattern in suspicious_patterns:
            if re.match(pattern, process_name):
                return True
        
        return False
    
    def _is_suspicious_filename(self, file_path: str) -> bool:
        """
        التحقق من كون اسم الملف مشبوه
        """
        filename = os.path.basename(file_path).lower()
        
        # أسماء ملفات مشبوهة
        suspicious_names = [
            'virus', 'malware', 'trojan', 'keylogger', 'backdoor',
            'rootkit', 'spyware', 'adware', 'ransomware'
        ]
        
        for name in suspicious_names:
            if name in filename:
                return True
        
        # امتدادات مشبوهة في مجلدات مؤقتة
        if 'temp' in file_path.lower() or 'tmp' in file_path.lower():
            suspicious_extensions = ['.exe', '.bat', '.cmd', '.scr', '.pif']
            file_ext = os.path.splitext(filename)[1]
            if file_ext in suspicious_extensions:
                return True
        
        return False
    
    def _is_malicious_domain(self, ip_address: str) -> bool:
        """
        التحقق من كون النطاق خبيث
        """
        return ip_address in self.malicious_domains
    
    def _create_threat_record(self, threat_type: str, threat_name: str,
                            file_path: str = None, process_name: str = None,
                            severity: str = 'MEDIUM', additional_info: Dict = None) -> Dict:
        """
        إنشاء سجل تهديد
        """
        threat = {
            'threat_type': threat_type,
            'threat_name': threat_name,
            'file_path': file_path,
            'process_name': process_name,
            'severity': severity,
            'detected_at': datetime.now(),
            'status': 'DETECTED',
            'additional_info': json.dumps(additional_info) if additional_info else None
        }
        
        # حفظ في قاعدة البيانات
        try:
            query = """
                INSERT INTO detected_threats (
                    threat_type, threat_name, file_path, process_name,
                    severity, status, additional_info
                ) VALUES (?, ?, ?, ?, ?, ?, ?)
            """
            
            self.db_manager.execute_query(query, (
                threat_type, threat_name, file_path, process_name,
                severity, 'DETECTED', threat['additional_info']
            ))
            
        except Exception as e:
            logger.error(f"❌ خطأ في حفظ سجل التهديد: {e}")
        
        return threat
    
    def _load_malware_signatures(self) -> Set[str]:
        """
        تحميل توقيعات البرمجيات الخبيثة
        """
        # توقيعات أساسية للاختبار
        return {
            'EICAR-STANDARD-ANTIVIRUS-TEST-FILE',
            'X5O!P%@AP[4\\PZX54(P^)7CC)7}$EICAR',
            'malware_signature_test',
            'virus_test_string'
        }
    
    def _load_suspicious_processes(self) -> Set[str]:
        """
        تحميل قائمة العمليات المشبوهة
        """
        return {
            'virus.exe', 'malware.exe', 'trojan.exe', 'keylogger.exe',
            'backdoor.exe', 'rootkit.exe', 'spyware.exe', 'adware.exe'
        }
    
    def _load_malicious_domains(self) -> Set[str]:
        """
        تحميل قائمة النطاقات الخبيثة
        """
        return {
            '127.0.0.1',  # للاختبار
            '0.0.0.0'     # للاختبار
        }
    
    def _load_threat_patterns(self) -> List[str]:
        """
        تحميل أنماط التهديدات
        """
        return [
            r'eval\s*\(\s*base64_decode',
            r'system\s*\(\s*["\'].*["\']',
            r'exec\s*\(\s*["\'].*["\']',
            r'shell_exec\s*\(',
            r'passthru\s*\(',
            r'<script.*>.*</script>',
            r'javascript:.*',
            r'vbscript:.*'
        ]
    
    def get_scan_statistics(self) -> Dict:
        """
        الحصول على إحصائيات الفحص
        """
        return self.scan_stats.copy()
    
    def get_detected_threats(self, limit: int = 100) -> List[Dict]:
        """
        الحصول على التهديدات المكتشفة
        """
        try:
            query = """
                SELECT * FROM detected_threats 
                ORDER BY detected_at DESC 
                LIMIT ?
            """
            return self.db_manager.execute_query(query, (limit,))
        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على التهديدات: {e}")
            return []
