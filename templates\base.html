<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Cyber Shield - Cyper Member | State of Palestine{% endblock %}</title>
    
    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- Socket.IO -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.7.2/socket.io.js"></script>
    
    <!-- Custom CSS -->
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
    
    {% block extra_head %}{% endblock %}
</head>
<body class="bg-light">
    <!-- Navigation -->
    {% if session.user_id %}
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark shadow-sm">
        <div class="container-fluid">
            <!-- Brand -->
            <a class="navbar-brand d-flex align-items-center" href="{{ url_for('dashboard') }}">
                <i class="fas fa-shield-alt text-success me-2"></i>
                <span class="fw-bold">Cyber Shield</span>
                <small class="text-muted ms-2">🇵🇸</small>
            </a>
            
            <!-- Toggle button -->
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <!-- Navigation items -->
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('dashboard') }}">
                            <i class="fas fa-tachometer-alt me-1"></i>
                            لوحة المعلومات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('security') }}">
                            <i class="fas fa-shield-virus me-1"></i>
                            الأمان
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('location') }}">
                            <i class="fas fa-map-marked-alt me-1"></i>
                            المواقع
                        </a>
                    </li>
                    {% if session.is_admin %}
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('admin') }}">
                            <i class="fas fa-cogs me-1"></i>
                            الإدارة
                        </a>
                    </li>
                    {% endif %}
                </ul>
                
                <!-- User menu -->
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle me-1"></i>
                            {{ session.username }}
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="#"><i class="fas fa-user me-2"></i>الملف الشخصي</a></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-cog me-2"></i>الإعدادات</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ url_for('logout') }}"><i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
    {% endif %}
    
    <!-- Main Content -->
    <main class="{% if session.user_id %}container-fluid mt-4{% else %}{% endif %}">
        <!-- Flash Messages -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                <div class="container-fluid">
                    {% for category, message in messages %}
                        <div class="alert alert-{{ 'danger' if category == 'error' else 'success' if category == 'success' else 'info' }} alert-dismissible fade show" role="alert">
                            <i class="fas fa-{{ 'exclamation-triangle' if category == 'error' else 'check-circle' if category == 'success' else 'info-circle' }} me-2"></i>
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    {% endfor %}
                </div>
            {% endif %}
        {% endwith %}
        
        <!-- Page Content -->
        {% block content %}{% endblock %}
    </main>
    
    <!-- Footer -->
    {% if session.user_id %}
    <footer class="bg-dark text-light text-center py-3 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <p class="mb-0">
                        <i class="fas fa-shield-alt text-success me-1"></i>
                        <strong>Cyber Shield</strong> - نظام الحماية السيبرانية
                    </p>
                </div>
                <div class="col-md-6">
                    <p class="mb-0">
                        🇵🇸 <strong>Cyper Member | State of Palestine</strong> - 2025
                    </p>
                </div>
            </div>
            <hr class="my-2">
            <small class="text-muted">
                صنع بـ ❤️ في فلسطين | من النهر إلى البحر فلسطين حرة
            </small>
        </div>
    </footer>
    {% endif %}
    
    <!-- Notification Container -->
    <div id="notification-container" class="position-fixed top-0 end-0 p-3" style="z-index: 9999;"></div>
    
    <!-- Loading Overlay -->
    <div id="loading-overlay" class="position-fixed top-0 start-0 w-100 h-100 d-none" style="background: rgba(0,0,0,0.7); z-index: 9998;">
        <div class="d-flex justify-content-center align-items-center h-100">
            <div class="text-center text-white">
                <div class="spinner-border text-success mb-3" role="status" style="width: 3rem; height: 3rem;">
                    <span class="visually-hidden">جاري التحميل...</span>
                </div>
                <h5>جاري التحميل...</h5>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JavaScript -->
    <script src="{{ url_for('static', filename='js/app.js') }}"></script>
    
    {% block extra_scripts %}{% endblock %}
    
    <!-- Socket.IO Connection -->
    {% if session.user_id %}
    <script>
        // إعداد Socket.IO
        const socket = io();
        
        socket.on('connect', function() {
            console.log('متصل بالخادم');
            showNotification('تم الاتصال بالخادم', 'success');
        });
        
        socket.on('disconnect', function() {
            console.log('انقطع الاتصال بالخادم');
            showNotification('انقطع الاتصال بالخادم', 'warning');
        });
        
        // استقبال التنبيهات
        socket.on('notification', function(data) {
            showNotification(data.message, data.severity, data.title);
        });
        
        // تحديثات الأمان
        socket.on('security_update', function(data) {
            showNotification(data.message, 'info', 'تحديث أمني');
            // تحديث واجهة الأمان إذا كانت مفتوحة
            if (window.location.pathname === '/security') {
                location.reload();
            }
        });
        
        // تقدم الفحص
        socket.on('scan_progress', function(data) {
            updateScanProgress(data.progress);
        });
    </script>
    {% endif %}
</body>
</html>
