/* Cyber Shield - Custom Styles */

/* Arabic Font */
body {
    font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
}

/* Palestinian Colors */
:root {
    --palestine-black: #000000;
    --palestine-white: #ffffff;
    --palestine-red: #ce1126;
    --palestine-green: #007a3d;
    --primary-color: var(--palestine-green);
    --secondary-color: var(--palestine-black);
    --accent-color: var(--palestine-red);
}

/* Navigation */
.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
}

.navbar-nav .nav-link {
    font-weight: 500;
    transition: all 0.3s ease;
}

.navbar-nav .nav-link:hover {
    color: var(--palestine-green) !important;
    transform: translateY(-1px);
}

/* Cards */
.card {
    border: none;
    border-radius: 15px;
    transition: all 0.3s ease;
    overflow: hidden;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15) !important;
}

.card-header {
    background: linear-gradient(135deg, var(--palestine-green), #28a745);
    color: white;
    border: none;
    font-weight: 600;
}

/* Buttons */
.btn {
    border-radius: 10px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.btn-primary {
    background: linear-gradient(135deg, var(--palestine-green), #28a745);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #28a745, var(--palestine-green));
}

.btn-danger {
    background: linear-gradient(135deg, var(--palestine-red), #dc3545);
    border: none;
}

.btn-danger:hover {
    background: linear-gradient(135deg, #dc3545, var(--palestine-red));
}

/* Progress Bars */
.progress {
    border-radius: 10px;
    overflow: hidden;
}

.progress-bar {
    background: linear-gradient(90deg, var(--palestine-green), #28a745);
}

/* Alerts */
.alert {
    border: none;
    border-radius: 10px;
    border-right: 4px solid;
}

.alert-success {
    background: rgba(40, 167, 69, 0.1);
    border-right-color: var(--palestine-green);
    color: var(--palestine-green);
}

.alert-danger {
    background: rgba(220, 53, 69, 0.1);
    border-right-color: var(--palestine-red);
    color: var(--palestine-red);
}

.alert-warning {
    background: rgba(255, 193, 7, 0.1);
    border-right-color: #ffc107;
    color: #856404;
}

.alert-info {
    background: rgba(13, 202, 240, 0.1);
    border-right-color: #0dcaf0;
    color: #055160;
}

/* Badges */
.badge {
    border-radius: 8px;
    font-weight: 500;
}

/* Tables */
.table {
    border-radius: 10px;
    overflow: hidden;
}

.table thead th {
    background: var(--palestine-green);
    color: white;
    border: none;
    font-weight: 600;
}

/* Forms */
.form-control {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: var(--palestine-green);
    box-shadow: 0 0 0 0.2rem rgba(0, 122, 61, 0.25);
}

/* Statistics Cards */
.stat-card {
    background: linear-gradient(135deg, #ffffff, #f8f9fa);
    border-radius: 15px;
    padding: 1.5rem;
    border-right: 4px solid var(--palestine-green);
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    background: linear-gradient(135deg, var(--palestine-green), #28a745);
}

/* Activity Timeline */
.activity-item {
    position: relative;
    padding-right: 2rem;
    margin-bottom: 1rem;
}

.activity-item::before {
    content: '';
    position: absolute;
    right: 0;
    top: 0.5rem;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background: var(--palestine-green);
}

.activity-item::after {
    content: '';
    position: absolute;
    right: 4px;
    top: 1.5rem;
    width: 2px;
    height: calc(100% + 0.5rem);
    background: #e9ecef;
}

.activity-item:last-child::after {
    display: none;
}

/* Security Status */
.security-status {
    text-align: center;
    padding: 2rem;
}

.security-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
}

.security-active {
    color: var(--palestine-green);
}

.security-inactive {
    color: var(--palestine-red);
}

/* Map Container */
.map-container {
    border-radius: 15px;
    overflow: hidden;
    height: 400px;
    border: 2px solid #e9ecef;
}

/* Loading Spinner */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Notification Toast */
.toast {
    border-radius: 10px;
    border: none;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.toast-header {
    background: var(--palestine-green);
    color: white;
    border: none;
}

/* Palestinian Flag Animation */
.flag-wave {
    animation: wave 3s ease-in-out infinite;
}

@keyframes wave {
    0%, 100% { transform: rotate(0deg); }
    25% { transform: rotate(3deg); }
    75% { transform: rotate(-3deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .card {
        margin-bottom: 1rem;
    }
    
    .stat-card {
        text-align: center;
        margin-bottom: 1rem;
    }
    
    .navbar-brand {
        font-size: 1.2rem;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    body {
        background-color: #1a1a1a;
        color: #ffffff;
    }
    
    .card {
        background-color: #2d2d2d;
        color: #ffffff;
    }
    
    .bg-light {
        background-color: #2d2d2d !important;
    }
    
    .text-muted {
        color: #adb5bd !important;
    }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: var(--palestine-green);
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    background: #28a745;
}

/* Animations */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in-right {
    animation: slideInRight 0.5s ease-out;
}

@keyframes slideInRight {
    from { transform: translateX(100%); }
    to { transform: translateX(0); }
}

/* Palestinian Theme Elements */
.palestine-gradient {
    background: linear-gradient(135deg, var(--palestine-black), var(--palestine-green), var(--palestine-white), var(--palestine-red));
}

.palestine-border {
    border-image: linear-gradient(90deg, var(--palestine-black), var(--palestine-green), var(--palestine-white), var(--palestine-red)) 1;
}

/* Status Indicators */
.status-online {
    color: var(--palestine-green);
}

.status-offline {
    color: var(--palestine-red);
}

.status-warning {
    color: #ffc107;
}

/* Footer */
footer {
    background: linear-gradient(135deg, var(--palestine-black), var(--palestine-green));
    color: white;
}

footer a {
    color: #ffffff;
    text-decoration: none;
    transition: color 0.3s ease;
}

footer a:hover {
    color: #28a745;
}

/* Special Effects */
.glow {
    box-shadow: 0 0 20px rgba(0, 122, 61, 0.5);
}

.pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

/* Print Styles */
@media print {
    .navbar, .btn, footer {
        display: none !important;
    }
    
    .card {
        border: 1px solid #000 !important;
        box-shadow: none !important;
    }
}
