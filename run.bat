@echo off
chcp 65001 > nul
title Cyber Shield - Cyper Member | State of Palestine

echo.
echo ===============================================
echo 🇵🇸 Cyber Shield - Cyper Member 🇵🇸
echo State of Palestine
echo ===============================================
echo.

echo 🔍 التحقق من Python...
python --version > nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت أو غير موجود في PATH
    echo يرجى تثبيت Python 3.8 أو أحدث من: https://python.org
    pause
    exit /b 1
)

echo ✅ تم العثور على Python

echo.
echo 📦 تثبيت المتطلبات...
python -m pip install -r requirements.txt

if errorlevel 1 (
    echo ❌ فشل في تثبيت المتطلبات
    pause
    exit /b 1
)

echo ✅ تم تثبيت المتطلبات بنجاح

echo.
echo 🚀 تشغيل Cyber Shield...
echo.

python main.py

if errorlevel 1 (
    echo.
    echo ❌ حدث خطأ في تشغيل التطبيق
    pause
    exit /b 1
)

echo.
echo ✅ تم إغلاق التطبيق بنجاح
pause
