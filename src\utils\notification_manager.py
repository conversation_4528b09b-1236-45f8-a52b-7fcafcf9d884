# -*- coding: utf-8 -*-
"""
Cyber Shield - Notification Manager
مدير التنبيهات والإشعارات
"""

import logging
import smtplib
import threading
import time
from typing import Dict, List, Optional, Callable
from datetime import datetime
from email.mime.text import MIMEText
from email.mime.multipart import MIME<PERSON><PERSON><PERSON><PERSON>
from email.mime.base import MIMEBase
from email import encoders
import json

from src.database.db_manager import DatabaseManager
from config.settings import *

logger = logging.getLogger(__name__)

class NotificationManager:
    """
    مدير التنبيهات والإشعارات
    """
    
    def __init__(self):
        self.db_manager = DatabaseManager()
        self.notification_queue = []
        self.is_processing = False
        self.process_thread = None
        self.callbacks = []
        
        # إعدادات التنبيهات
        self.email_enabled = EMAIL_NOTIFICATIONS_ENABLED if 'EMAIL_NOTIFICATIONS_ENABLED' in globals() else True
        self.desktop_enabled = DESKTOP_NOTIFICATIONS_ENABLED if 'DESKTOP_NOTIFICATIONS_ENABLED' in globals() else True
        self.sound_enabled = SOUND_ALERTS_ENABLED if 'SOUND_ALERTS_ENABLED' in globals() else True
        
        # قوالب الرسائل
        self.message_templates = self._load_message_templates()
        
        # بدء معالج التنبيهات
        self.start_notification_processor()
    
    def send_notification(self, notification_type: str, title: str, message: str, 
                         severity: str = 'INFO', recipient: str = None, 
                         additional_data: Dict = None):
        """
        إرسال تنبيه
        """
        try:
            notification = {
                'id': self._generate_notification_id(),
                'type': notification_type,
                'title': title,
                'message': message,
                'severity': severity,
                'recipient': recipient,
                'additional_data': additional_data or {},
                'created_at': datetime.now(),
                'status': 'PENDING'
            }
            
            # إضافة للقائمة
            self.notification_queue.append(notification)
            
            # حفظ في قاعدة البيانات
            self._save_notification(notification)
            
            logger.info(f"📢 تم إضافة تنبيه جديد: {title}")
            
        except Exception as e:
            logger.error(f"❌ خطأ في إرسال التنبيه: {e}")
    
    def send_security_alert(self, threat_type: str, threat_name: str, 
                           severity: str, details: Dict = None):
        """
        إرسال تنبيه أمني
        """
        try:
            # تحديد مستوى الخطورة
            severity_icons = {
                'LOW': '🟡',
                'MEDIUM': '🟠', 
                'HIGH': '🔴',
                'CRITICAL': '🚨'
            }
            
            icon = severity_icons.get(severity, '⚠️')
            title = f"{icon} تنبيه أمني - {severity}"
            
            # إنشاء الرسالة
            message = f"""
تم اكتشاف تهديد أمني جديد:

نوع التهديد: {threat_type}
اسم التهديد: {threat_name}
مستوى الخطورة: {severity}
وقت الاكتشاف: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

{self._format_threat_details(details) if details else ''}

يرجى اتخاذ الإجراءات اللازمة فوراً.

---
Cyber Shield - Cyper Member | State of Palestine
            """.strip()
            
            # إرسال التنبيه
            self.send_notification(
                'SECURITY_ALERT',
                title,
                message,
                severity,
                additional_data={
                    'threat_type': threat_type,
                    'threat_name': threat_name,
                    'details': details
                }
            )
            
            # تشغيل صوت التنبيه للتهديدات عالية الخطورة
            if severity in ['HIGH', 'CRITICAL'] and self.sound_enabled:
                self._play_alert_sound()
            
        except Exception as e:
            logger.error(f"❌ خطأ في إرسال التنبيه الأمني: {e}")
    
    def send_system_notification(self, event_type: str, message: str, 
                                severity: str = 'INFO', details: Dict = None):
        """
        إرسال تنبيه نظام
        """
        try:
            event_icons = {
                'SYSTEM_START': '🚀',
                'SYSTEM_STOP': '🛑',
                'BACKUP_COMPLETE': '💾',
                'UPDATE_AVAILABLE': '🔄',
                'ERROR': '❌',
                'WARNING': '⚠️'
            }
            
            icon = event_icons.get(event_type, '📢')
            title = f"{icon} {event_type.replace('_', ' ').title()}"
            
            self.send_notification(
                'SYSTEM_NOTIFICATION',
                title,
                message,
                severity,
                additional_data={
                    'event_type': event_type,
                    'details': details
                }
            )
            
        except Exception as e:
            logger.error(f"❌ خطأ في إرسال تنبيه النظام: {e}")
    
    def send_user_notification(self, user_id: int, title: str, message: str,
                              notification_type: str = 'INFO'):
        """
        إرسال تنبيه لمستخدم محدد
        """
        try:
            # الحصول على بيانات المستخدم
            user = self.db_manager.get_user_by_id(user_id)
            if not user:
                logger.error(f"❌ المستخدم {user_id} غير موجود")
                return
            
            self.send_notification(
                'USER_NOTIFICATION',
                title,
                message,
                'INFO',
                recipient=user['email'],
                additional_data={
                    'user_id': user_id,
                    'username': user['username']
                }
            )
            
        except Exception as e:
            logger.error(f"❌ خطأ في إرسال تنبيه المستخدم: {e}")
    
    def send_email_notification(self, recipient: str, subject: str, body: str,
                               html_body: str = None, attachments: List[str] = None):
        """
        إرسال إشعار بريد إلكتروني
        """
        try:
            if not self.email_enabled or not EMAIL_SENDER or not EMAIL_PASSWORD:
                logger.warning("⚠️ إعدادات البريد الإلكتروني غير مكتملة")
                return False
            
            # إنشاء الرسالة
            msg = MIMEMultipart('alternative')
            msg['From'] = EMAIL_SENDER
            msg['To'] = recipient
            msg['Subject'] = subject
            
            # إضافة النص العادي
            text_part = MIMEText(body, 'plain', 'utf-8')
            msg.attach(text_part)
            
            # إضافة HTML إذا توفر
            if html_body:
                html_part = MIMEText(html_body, 'html', 'utf-8')
                msg.attach(html_part)
            
            # إضافة المرفقات
            if attachments:
                for file_path in attachments:
                    if os.path.exists(file_path):
                        with open(file_path, "rb") as attachment:
                            part = MIMEBase('application', 'octet-stream')
                            part.set_payload(attachment.read())
                        
                        encoders.encode_base64(part)
                        part.add_header(
                            'Content-Disposition',
                            f'attachment; filename= {os.path.basename(file_path)}'
                        )
                        msg.attach(part)
            
            # إرسال البريد
            server = smtplib.SMTP(SMTP_SERVER, SMTP_PORT)
            server.starttls()
            server.login(EMAIL_SENDER, EMAIL_PASSWORD)
            text = msg.as_string()
            server.sendmail(EMAIL_SENDER, recipient, text)
            server.quit()
            
            logger.info(f"📧 تم إرسال بريد إلكتروني إلى: {recipient}")
            return True
            
        except Exception as e:
            logger.error(f"❌ خطأ في إرسال البريد الإلكتروني: {e}")
            return False
    
    def send_desktop_notification(self, title: str, message: str, 
                                 icon: str = None, timeout: int = 5):
        """
        إرسال إشعار سطح المكتب
        """
        try:
            if not self.desktop_enabled:
                return
            
            # محاولة استخدام مكتبات مختلفة للإشعارات
            try:
                # Windows
                if os.name == 'nt':
                    try:
                        from win10toast import ToastNotifier
                        toaster = ToastNotifier()
                        toaster.show_toast(
                            title,
                            message,
                            icon_path=icon,
                            duration=timeout,
                            threaded=True
                        )
                        return True
                    except ImportError:
                        pass
                
                # Linux/macOS
                try:
                    from plyer import notification
                    notification.notify(
                        title=title,
                        message=message,
                        app_icon=icon,
                        timeout=timeout
                    )
                    return True
                except ImportError:
                    pass
                
                # Fallback - طباعة في وحدة التحكم
                logger.info(f"🔔 {title}: {message}")
                return True
                
            except Exception as e:
                logger.debug(f"خطأ في إشعار سطح المكتب: {e}")
                return False
                
        except Exception as e:
            logger.error(f"❌ خطأ في إرسال إشعار سطح المكتب: {e}")
            return False
    
    def start_notification_processor(self):
        """
        بدء معالج التنبيهات
        """
        if not self.is_processing:
            self.is_processing = True
            self.process_thread = threading.Thread(target=self._process_notifications, daemon=True)
            self.process_thread.start()
            logger.info("📢 تم بدء معالج التنبيهات")
    
    def stop_notification_processor(self):
        """
        إيقاف معالج التنبيهات
        """
        self.is_processing = False
        if self.process_thread:
            self.process_thread.join(timeout=5)
        logger.info("🛑 تم إيقاف معالج التنبيهات")
    
    def _process_notifications(self):
        """
        معالجة قائمة التنبيهات
        """
        while self.is_processing:
            try:
                if self.notification_queue:
                    notification = self.notification_queue.pop(0)
                    self._handle_notification(notification)
                else:
                    time.sleep(1)  # انتظار ثانية واحدة
                    
            except Exception as e:
                logger.error(f"❌ خطأ في معالجة التنبيهات: {e}")
                time.sleep(5)
    
    def _handle_notification(self, notification: Dict):
        """
        معالجة تنبيه واحد
        """
        try:
            # إرسال إشعار سطح المكتب
            if self.desktop_enabled:
                self.send_desktop_notification(
                    notification['title'],
                    notification['message']
                )
            
            # إرسال بريد إلكتروني إذا كان هناك مستقبل
            if notification.get('recipient') and self.email_enabled:
                html_body = self._generate_html_email(notification)
                self.send_email_notification(
                    notification['recipient'],
                    notification['title'],
                    notification['message'],
                    html_body
                )
            
            # إشعار المستمعين
            self._notify_callbacks(notification)
            
            # تحديث حالة التنبيه
            notification['status'] = 'SENT'
            self._update_notification_status(notification['id'], 'SENT')
            
        except Exception as e:
            logger.error(f"❌ خطأ في معالجة التنبيه: {e}")
            notification['status'] = 'FAILED'
            self._update_notification_status(notification['id'], 'FAILED')
    
    def _generate_html_email(self, notification: Dict) -> str:
        """
        إنشاء بريد إلكتروني HTML
        """
        try:
            severity_colors = {
                'INFO': '#2196F3',
                'WARNING': '#FF9800',
                'ERROR': '#F44336',
                'CRITICAL': '#9C27B0'
            }
            
            color = severity_colors.get(notification['severity'], '#2196F3')
            
            html_template = f"""
            <!DOCTYPE html>
            <html dir="rtl" lang="ar">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>{notification['title']}</title>
                <style>
                    body {{
                        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                        background-color: #f5f5f5;
                        margin: 0;
                        padding: 20px;
                        direction: rtl;
                    }}
                    .container {{
                        max-width: 600px;
                        margin: 0 auto;
                        background-color: white;
                        border-radius: 10px;
                        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                        overflow: hidden;
                    }}
                    .header {{
                        background: linear-gradient(135deg, #000000, {color});
                        color: white;
                        padding: 20px;
                        text-align: center;
                    }}
                    .header h1 {{
                        margin: 0;
                        font-size: 24px;
                    }}
                    .flag {{
                        font-size: 30px;
                        margin-bottom: 10px;
                    }}
                    .content {{
                        padding: 30px;
                    }}
                    .message {{
                        background-color: #f8f9fa;
                        border-left: 4px solid {color};
                        padding: 15px;
                        margin: 20px 0;
                        border-radius: 5px;
                    }}
                    .footer {{
                        background-color: #f8f9fa;
                        padding: 20px;
                        text-align: center;
                        color: #666;
                        font-size: 12px;
                    }}
                    .severity {{
                        display: inline-block;
                        padding: 5px 10px;
                        border-radius: 15px;
                        color: white;
                        background-color: {color};
                        font-size: 12px;
                        font-weight: bold;
                    }}
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <div class="flag">🇵🇸</div>
                        <h1>Cyber Shield</h1>
                        <p>Cyper Member | State of Palestine</p>
                    </div>
                    
                    <div class="content">
                        <h2>{notification['title']}</h2>
                        <span class="severity">{notification['severity']}</span>
                        
                        <div class="message">
                            {notification['message'].replace(chr(10), '<br>')}
                        </div>
                        
                        <p><strong>وقت الإرسال:</strong> {notification['created_at'].strftime('%Y-%m-%d %H:%M:%S')}</p>
                    </div>
                    
                    <div class="footer">
                        <p>هذه رسالة تلقائية من نظام Cyber Shield</p>
                        <p>© 2025 Cyper Member | State of Palestine</p>
                    </div>
                </div>
            </body>
            </html>
            """
            
            return html_template
            
        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء HTML البريد الإلكتروني: {e}")
            return notification['message']
    
    def _format_threat_details(self, details: Dict) -> str:
        """
        تنسيق تفاصيل التهديد
        """
        try:
            formatted = []
            
            if details.get('file_path'):
                formatted.append(f"مسار الملف: {details['file_path']}")
            
            if details.get('process_name'):
                formatted.append(f"اسم العملية: {details['process_name']}")
            
            if details.get('ip_address'):
                formatted.append(f"عنوان IP: {details['ip_address']}")
            
            if details.get('location'):
                formatted.append(f"الموقع: {details['location']}")
            
            return '\n'.join(formatted)
            
        except Exception as e:
            logger.error(f"❌ خطأ في تنسيق تفاصيل التهديد: {e}")
            return ""
    
    def _play_alert_sound(self):
        """
        تشغيل صوت التنبيه
        """
        try:
            if not self.sound_enabled:
                return
            
            # محاولة تشغيل صوت النظام
            if os.name == 'nt':  # Windows
                import winsound
                winsound.MessageBeep(winsound.MB_ICONEXCLAMATION)
            else:  # Linux/macOS
                os.system('echo -e "\a"')  # Bell sound
                
        except Exception as e:
            logger.debug(f"خطأ في تشغيل صوت التنبيه: {e}")
    
    def _generate_notification_id(self) -> str:
        """
        إنشاء معرف فريد للتنبيه
        """
        import uuid
        return str(uuid.uuid4())
    
    def _save_notification(self, notification: Dict):
        """
        حفظ التنبيه في قاعدة البيانات
        """
        try:
            # إنشاء جدول التنبيهات إذا لم يكن موجوداً
            self._create_notifications_table()
            
            query = """
                INSERT INTO notifications (
                    id, type, title, message, severity, recipient,
                    additional_data, created_at, status
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            
            self.db_manager.execute_query(query, (
                notification['id'],
                notification['type'],
                notification['title'],
                notification['message'],
                notification['severity'],
                notification['recipient'],
                json.dumps(notification['additional_data']),
                notification['created_at'],
                notification['status']
            ))
            
        except Exception as e:
            logger.error(f"❌ خطأ في حفظ التنبيه: {e}")
    
    def _create_notifications_table(self):
        """
        إنشاء جدول التنبيهات
        """
        try:
            query = """
                CREATE TABLE IF NOT EXISTS notifications (
                    id TEXT PRIMARY KEY,
                    type TEXT NOT NULL,
                    title TEXT NOT NULL,
                    message TEXT NOT NULL,
                    severity TEXT DEFAULT 'INFO',
                    recipient TEXT,
                    additional_data TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    status TEXT DEFAULT 'PENDING'
                )
            """
            
            self.db_manager.execute_query(query)
            
        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء جدول التنبيهات: {e}")
    
    def _update_notification_status(self, notification_id: str, status: str):
        """
        تحديث حالة التنبيه
        """
        try:
            query = "UPDATE notifications SET status = ? WHERE id = ?"
            self.db_manager.execute_query(query, (status, notification_id))
        except Exception as e:
            logger.error(f"❌ خطأ في تحديث حالة التنبيه: {e}")
    
    def _load_message_templates(self) -> Dict:
        """
        تحميل قوالب الرسائل
        """
        return {
            'security_alert': "تم اكتشاف تهديد أمني: {threat_name}",
            'system_error': "خطأ في النظام: {error_message}",
            'user_login': "تم تسجيل دخول المستخدم: {username}",
            'backup_complete': "تم إنشاء نسخة احتياطية بنجاح"
        }
    
    def add_callback(self, callback: Callable):
        """
        إضافة مستمع للتنبيهات
        """
        self.callbacks.append(callback)
    
    def _notify_callbacks(self, notification: Dict):
        """
        إشعار المستمعين
        """
        for callback in self.callbacks:
            try:
                callback(notification)
            except Exception as e:
                logger.error(f"❌ خطأ في استدعاء callback: {e}")
    
    def get_notifications(self, limit: int = 100, status: str = None) -> List[Dict]:
        """
        الحصول على التنبيهات
        """
        try:
            query = "SELECT * FROM notifications"
            params = []
            
            if status:
                query += " WHERE status = ?"
                params.append(status)
            
            query += " ORDER BY created_at DESC LIMIT ?"
            params.append(limit)
            
            return self.db_manager.execute_query(query, tuple(params))
            
        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على التنبيهات: {e}")
            return []
