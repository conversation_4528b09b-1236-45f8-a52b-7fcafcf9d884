/**
 * Cyber Shield - Main JavaScript Application
 * تطبيق JavaScript الرئيسي لـ Cyber Shield
 */

// Global Variables
let socket = null;
let notificationCount = 0;

// Initialize Application
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

function initializeApp() {
    console.log('🚀 تهيئة Cyber Shield Web App');

    // Initialize tooltips
    initializeTooltips();

    // Initialize notifications
    initializeNotifications();

    // Initialize real-time updates
    initializeRealTimeUpdates();

    // Initialize page-specific features
    initializePageFeatures();

    console.log('✅ تم تهيئة التطبيق بنجاح');
}

function initializeTooltips() {
    // Initialize Bootstrap tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

function initializeNotifications() {
    // Create notification container if it doesn't exist
    if (!document.getElementById('notification-container')) {
        const container = document.createElement('div');
        container.id = 'notification-container';
        container.className = 'position-fixed top-0 end-0 p-3';
        container.style.zIndex = '9999';
        document.body.appendChild(container);
    }
}

function initializeRealTimeUpdates() {
    // Initialize Socket.IO if available
    if (typeof io !== 'undefined') {
        socket = io();

        socket.on('connect', function() {
            console.log('🔗 متصل بالخادم');
            updateConnectionStatus(true);
        });

        socket.on('disconnect', function() {
            console.log('❌ انقطع الاتصال بالخادم');
            updateConnectionStatus(false);
        });

        socket.on('notification', function(data) {
            showNotification(data.message, data.severity, data.title);
        });

        socket.on('security_update', function(data) {
            handleSecurityUpdate(data);
        });

        socket.on('scan_progress', function(data) {
            updateScanProgress(data.progress);
        });
    }
}

function initializePageFeatures() {
    // Initialize page-specific features based on current page
    const currentPage = window.location.pathname;

    switch(currentPage) {
        case '/dashboard':
            initializeDashboard();
            break;
        case '/security':
            initializeSecurity();
            break;
        case '/location':
            initializeLocation();
            break;
        case '/admin':
            initializeAdmin();
            break;
    }
}

// Notification System
function showNotification(message, type = 'info', title = null) {
    const container = document.getElementById('notification-container');
    if (!container) return;

    const notificationId = 'notification-' + Date.now();
    const typeClass = getNotificationClass(type);
    const icon = getNotificationIcon(type);

    const notificationHtml = `
        <div id="${notificationId}" class="toast align-items-center text-white ${typeClass} border-0 fade-in" role="alert">
            <div class="d-flex">
                <div class="toast-body">
                    <div class="d-flex align-items-center">
                        <i class="${icon} me-2"></i>
                        <div>
                            ${title ? `<div class="fw-bold">${title}</div>` : ''}
                            <div>${message}</div>
                        </div>
                    </div>
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        </div>
    `;

    container.insertAdjacentHTML('beforeend', notificationHtml);

    const toastElement = document.getElementById(notificationId);
    const toast = new bootstrap.Toast(toastElement, {
        autohide: true,
        delay: type === 'error' ? 8000 : 5000
    });

    toast.show();

    // Remove element after hiding
    toastElement.addEventListener('hidden.bs.toast', function() {
        toastElement.remove();
    });

    // Play notification sound for important messages
    if (type === 'error' || type === 'warning') {
        playNotificationSound();
    }

    notificationCount++;
}

function getNotificationClass(type) {
    const classes = {
        'success': 'bg-success',
        'error': 'bg-danger',
        'warning': 'bg-warning',
        'info': 'bg-info',
        'primary': 'bg-primary'
    };
    return classes[type] || 'bg-info';
}

function getNotificationIcon(type) {
    const icons = {
        'success': 'fas fa-check-circle',
        'error': 'fas fa-exclamation-triangle',
        'warning': 'fas fa-exclamation-circle',
        'info': 'fas fa-info-circle',
        'primary': 'fas fa-bell'
    };
    return icons[type] || 'fas fa-bell';
}

function playNotificationSound() {
    try {
        // Create audio context for notification sound
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();
        const oscillator = audioContext.createOscillator();
        const gainNode = audioContext.createGain();

        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);

        oscillator.frequency.value = 800;
        oscillator.type = 'sine';

        gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.5);

        oscillator.start(audioContext.currentTime);
        oscillator.stop(audioContext.currentTime + 0.5);
    } catch (error) {
        console.log('لا يمكن تشغيل صوت التنبيه');
    }
}

// Loading System
function showLoading(show = true) {
    const overlay = document.getElementById('loading-overlay');
    if (overlay) {
        if (show) {
            overlay.classList.remove('d-none');
        } else {
            overlay.classList.add('d-none');
        }
    }
}

// Connection Status
function updateConnectionStatus(connected) {
    const statusElements = document.querySelectorAll('.connection-status');
    statusElements.forEach(element => {
        if (connected) {
            element.innerHTML = '<i class="fas fa-circle text-success me-1"></i>متصل';
            element.className = 'badge bg-success connection-status';
        } else {
            element.innerHTML = '<i class="fas fa-circle text-danger me-1"></i>غير متصل';
            element.className = 'badge bg-danger connection-status';
        }
    });
}

// Security Updates Handler
function handleSecurityUpdate(data) {
    showNotification(data.message, 'info', 'تحديث أمني');

    // Update security status indicators
    const statusElements = document.querySelectorAll('.security-status');
    statusElements.forEach(element => {
        if (data.type === 'protection_started') {
            element.innerHTML = '<span class="text-success">نشطة</span>';
        } else if (data.type === 'protection_stopped') {
            element.innerHTML = '<span class="text-danger">متوقفة</span>';
        }
    });
}

// Scan Progress Handler
function updateScanProgress(progress) {
    const progressBar = document.getElementById('scanProgress');
    const status = document.getElementById('scanStatus');
    const details = document.getElementById('scanDetails');

    if (progressBar) {
        progressBar.style.width = progress + '%';
        progressBar.setAttribute('aria-valuenow', progress);
    }

    if (status) {
        status.textContent = `تقدم الفحص: ${progress}%`;
    }

    if (details) {
        if (progress >= 100) {
            details.textContent = 'تم الانتهاء من الفحص';
        } else if (progress >= 75) {
            details.textContent = 'جاري فحص ملفات النظام...';
        } else if (progress >= 50) {
            details.textContent = 'جاري فحص ملفات المستخدم...';
        } else if (progress >= 25) {
            details.textContent = 'جاري فحص الذاكرة...';
        } else {
            details.textContent = 'جاري تحضير الفحص...';
        }
    }
}

// API Helper Functions
async function apiRequest(url, options = {}) {
    try {
        const defaultOptions = {
            headers: {
                'Content-Type': 'application/json',
            }
        };

        const response = await fetch(url, { ...defaultOptions, ...options });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        return await response.json();
    } catch (error) {
        console.error('API Request Error:', error);
        throw error;
    }
}

// Utility Functions
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-SA', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 بايت';

    const k = 1024;
    const sizes = ['بايت', 'كيلوبايت', 'ميجابايت', 'جيجابايت'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        showNotification('تم نسخ النص', 'success');
    }).catch(function(err) {
        console.error('فشل في النسخ: ', err);
        showNotification('فشل في نسخ النص', 'error');
    });
}

// Page-specific Initializers
function initializeDashboard() {
    console.log('🏠 تهيئة لوحة المعلومات');

    // Auto-refresh dashboard data every 30 seconds
    setInterval(refreshDashboardData, 30000);

    // Initialize charts if Chart.js is available
    if (typeof Chart !== 'undefined') {
        initializeDashboardCharts();
    }
}

function initializeSecurity() {
    console.log('🛡️ تهيئة صفحة الأمان');

    // Auto-refresh security status every 10 seconds
    setInterval(refreshSecurityStatus, 10000);
}

function initializeLocation() {
    console.log('🗺️ تهيئة صفحة المواقع');

    // Initialize map if available
    if (typeof L !== 'undefined') {
        initializeMap();
    }
}

function initializeAdmin() {
    console.log('⚙️ تهيئة لوحة الإدارة');

    // Initialize admin-specific features
    initializeUserManagement();
    initializeLogViewer();
}

function refreshDashboardData() {
    // Refresh dashboard data without page reload
    fetch('/api/dashboard/data')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateDashboardElements(data.data);
            }
        })
        .catch(error => {
            console.error('خطأ في تحديث بيانات لوحة المعلومات:', error);
        });
}

function refreshSecurityStatus() {
    // Refresh security status
    fetch('/api/security/status')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateSecurityElements(data.data);
            }
        })
        .catch(error => {
            console.error('خطأ في تحديث حالة الأمان:', error);
        });
}

// Export functions for global use
window.CyberShield = {
    showNotification,
    showLoading,
    apiRequest,
    formatDate,
    formatFileSize,
    copyToClipboard
};